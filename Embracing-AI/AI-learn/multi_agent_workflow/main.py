"""
异步多智能体需求测试工作流系统 - 主程序入口
"""
import asyncio
import os
from datetime import datetime
from typing import Optional

from config.settings import settings
from storage.models import RequirementDocument, Priority
from storage.simple_chroma_manager import simple_chroma_manager as chroma_manager
from core.memory_manager import global_memory_manager
from core.events import global_event_bus, EventLogger
from core.model_client import get_model_client, ModelClientFactory, cleanup_model_client
from workflow.orchestrator import get_orchestrator


class MultiAgentWorkflowSystem:
    """多智能体工作流系统主类"""
    
    def __init__(self):
        self.model_client = None
        self.orchestrator = None
        self.event_logger = None
        self.initialized = False
    
    async def initialize(self):
        """初始化系统"""
        if self.initialized:
            return
        
        print("🚀 正在初始化多智能体工作流系统...")
        
        # 测试模型连接
        print("🔍 正在测试模型连接...")
        if not await ModelClientFactory.test_connection():
            raise Exception("模型连接测试失败，请检查配置")
        
        # 初始化模型客户端
        self.model_client = get_model_client()
        model_name = settings.ollama_model if settings.is_using_ollama else settings.openai_model
        print(f"✅ 模型客户端初始化完成: {model_name}")
        
        # 初始化ChromaDB
        await chroma_manager.initialize()
        print("✅ ChromaDB向量数据库初始化完成")
        
        # 初始化记忆管理器
        await global_memory_manager.initialize()
        print("✅ 智能体记忆系统初始化完成")
        
        # 初始化工作流编排器
        self.orchestrator = get_orchestrator(self.model_client)
        print("✅ 工作流编排器初始化完成")
        
        # 初始化事件日志
        self.event_logger = EventLogger(global_event_bus)
        print("✅ 事件系统初始化完成")
        
        self.initialized = True
        print("🎉 系统初始化完成！")
    
    async def process_requirement(self, title: str, content: str, 
                                created_by: str = "用户",
                                priority: Priority = Priority.MEDIUM,
                                tags: Optional[list] = None) -> dict:
        """处理需求文档的完整工作流"""
        if not self.initialized:
            await self.initialize()
        
        print(f"\n📋 开始处理需求: {title}")
        
        # 创建需求文档
        requirement = RequirementDocument(
            title=title,
            content=content,
            created_by=created_by,
            priority=priority,
            tags=tags or []
        )
        
        print(f"📝 需求文档已创建: ID={requirement.id}")
        
        # 启动工作流
        workflow_task = await self.orchestrator.start_workflow(
            requirement=requirement,
            callback=self._workflow_completed_callback
        )
        
        print(f"⚡ 工作流已启动: ID={workflow_task.id}")
        
        # 等待工作流完成（实际应用中可能需要异步处理）
        max_wait_time = 300  # 5分钟超时
        wait_time = 0
        
        while workflow_task.id in self.orchestrator.active_workflows and wait_time < max_wait_time:
            await asyncio.sleep(2)
            wait_time += 2
            
            # 获取最新状态
            current_task = await self.orchestrator.get_workflow_status(workflow_task.id)
            if current_task:
                print(f"⏳ 当前阶段: {current_task.current_stage.value} - 状态: {current_task.status.value}")
        
        # 获取最终结果
        if workflow_task.id not in self.orchestrator.active_workflows:
            print("✅ 工作流执行完成！")
            return {
                "success": True,
                "workflow_id": workflow_task.id,
                "requirement_id": requirement.id,
                "final_data": workflow_task.current_data
            }
        else:
            print("⚠️ 工作流执行超时")
            return {
                "success": False,
                "workflow_id": workflow_task.id,
                "requirement_id": requirement.id,
                "error": "执行超时"
            }
    
    async def _workflow_completed_callback(self, workflow_task):
        """工作流完成回调"""
        print(f"🎯 工作流 {workflow_task.id} 执行完成")
        print(f"📊 执行历史: {len(workflow_task.execution_history)} 个阶段")
        
        # 显示最终结果摘要
        final_result = workflow_task.current_data.get("final_result", {})
        if "summary" in final_result:
            summary = final_result["summary"]
            print(f"📈 测试用例总数: {summary.get('total_testcases', 0)}")
            print(f"✅ 通过评审数: {summary.get('approved_count', 0)}")
            print(f"📊 平均评分: {summary.get('average_score', 0):.1f}")
    
    async def get_system_status(self) -> dict:
        """获取系统状态"""
        if not self.initialized:
            return {"status": "未初始化"}
        
        # 获取工作流统计
        workflow_stats = await self.orchestrator.get_workflow_statistics()
        
        # 获取记忆统计
        memory_stats = await global_memory_manager.get_memory_statistics()
        
        # 获取ChromaDB统计
        chroma_stats = await chroma_manager.get_collection_stats()
        
        return {
            "status": "运行中",
            "workflow": workflow_stats,
            "memory": memory_stats,
            "storage": chroma_stats,
            "timestamp": datetime.now().isoformat()
        }
    
    async def cleanup(self):
        """清理系统资源"""
        print("🧹 正在清理系统资源...")
        
        if self.orchestrator:
            # 取消所有活跃工作流
            active_workflows = await self.orchestrator.get_active_workflows()
            for workflow in active_workflows:
                await self.orchestrator.cancel_workflow(workflow.id)
        
        if chroma_manager:
            await chroma_manager.cleanup()
        
        # 清理模型客户端
        await cleanup_model_client()
        
        print("✅ 系统资源清理完成")


async def demo_workflow():
    """演示工作流程"""
    system = MultiAgentWorkflowSystem()
    
    try:
        # 示例需求
        demo_requirements = [
            {
                "title": "用户登录功能",
                "content": """
实现用户登录功能，包括以下要求：
1. 用户可以使用用户名和密码登录
2. 支持记住登录状态
3. 登录失败时显示错误信息
4. 支持密码找回功能
5. 登录成功后跳转到主页面
                """,
                "priority": Priority.HIGH,
                "tags": ["用户管理", "安全", "前端"]
            },
            {
                "title": "商品搜索功能",
                "content": """
实现商品搜索功能，包括：
1. 支持关键词搜索
2. 支持分类筛选
3. 支持价格区间筛选
4. 搜索结果分页显示
5. 支持搜索历史记录
                """,
                "priority": Priority.MEDIUM,
                "tags": ["搜索", "商品", "用户体验"]
            }
        ]
        
        print("🎬 开始演示多智能体工作流...")
        
        for req_data in demo_requirements:
            result = await system.process_requirement(
                title=req_data["title"],
                content=req_data["content"],
                priority=req_data["priority"],
                tags=req_data["tags"]
            )
            
            print(f"\n📋 需求 '{req_data['title']}' 处理结果:")
            print(f"   成功: {result['success']}")
            print(f"   工作流ID: {result['workflow_id']}")
            
            if result["success"]:
                final_data = result.get("final_data", {})
                testcases = final_data.get("testcases", [])
                print(f"   生成测试用例数: {len(testcases)}")
            
            print("-" * 50)
        
        # 显示系统状态
        print("\n📊 系统状态:")
        status = await system.get_system_status()
        print(f"   活跃工作流: {status['workflow']['active_workflows']}")
        print(f"   存储集合: {list(status['storage'].keys())}")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await system.cleanup()


async def interactive_mode():
    """交互模式"""
    system = MultiAgentWorkflowSystem()
    
    try:
        await system.initialize()
        
        print("\n🎯 进入交互模式")
        print("输入 'quit' 退出，'status' 查看状态")
        
        while True:
            print("\n" + "="*50)
            title = input("请输入需求标题 (或 quit/status): ").strip()
            
            if title.lower() == 'quit':
                break
            elif title.lower() == 'status':
                status = await system.get_system_status()
                print(f"系统状态: {status}")
                continue
            elif not title:
                continue
            
            content = input("请输入需求内容: ").strip()
            if not content:
                continue
            
            priority_input = input("请输入优先级 (low/medium/high/critical, 默认medium): ").strip()
            try:
                priority = Priority(priority_input.lower()) if priority_input else Priority.MEDIUM
            except ValueError:
                priority = Priority.MEDIUM
            
            tags_input = input("请输入标签 (用逗号分隔, 可选): ").strip()
            tags = [tag.strip() for tag in tags_input.split(",")] if tags_input else []
            
            print(f"\n🚀 开始处理需求: {title}")
            
            result = await system.process_requirement(
                title=title,
                content=content,
                priority=priority,
                tags=tags
            )
            
            print(f"\n✅ 处理完成: {result}")
    
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 交互模式错误: {e}")
    finally:
        await system.cleanup()


async def main():
    """主函数"""
    print("🤖 异步多智能体需求测试工作流系统")
    print("=" * 50)
    
    # 检查模型配置
    if settings.is_using_ollama:
        print(f"🤖 使用Ollama本地模型: {settings.ollama_model}")
        print(f"📡 Ollama服务地址: {settings.ollama_base_url}")
    elif settings.is_using_openai and not settings.openai_api_key:
        print("❌ 使用OpenAI时请设置 OPENAI_API_KEY 环境变量")
        return
    elif settings.is_using_azure and not settings.azure_openai_api_key:
        print("❌ 使用Azure OpenAI时请设置 AZURE_OPENAI_API_KEY 环境变量")
        return
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        await demo_workflow()
    else:
        await interactive_mode()


if __name__ == "__main__":
    asyncio.run(main())