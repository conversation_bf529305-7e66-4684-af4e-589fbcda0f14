"""
简化的ChromaDB管理器 - 不依赖SentenceTransformer
"""
import os
import asyncio
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings as ChromaSettings

# 直接导入配置
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.settings import settings


class SimpleChromaDBManager:
    """简化的ChromaDB管理器"""
    
    def __init__(self):
        self.client = None
        self.collections = {}
        self.initialized = False
    
    async def initialize(self):
        """初始化ChromaDB客户端"""
        if self.initialized:
            return
        
        try:
            # 创建数据目录
            os.makedirs(settings.chroma_db_path, exist_ok=True)
            
            # 初始化ChromaDB客户端（使用默认嵌入函数）
            self.client = chromadb.PersistentClient(
                path=settings.chroma_db_path,
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 创建默认集合
            await self._create_collections()
            
            self.initialized = True
            print(f"✅ ChromaDB初始化成功: {settings.chroma_db_path}")
            
        except Exception as e:
            print(f"❌ ChromaDB初始化失败: {e}")
            raise
    
    async def _create_collections(self):
        """创建必要的集合"""
        collection_names = [
            "requirements",
            "testcases", 
            "reviews",
            "agent_memory"
        ]
        
        for name in collection_names:
            try:
                collection = self.client.get_or_create_collection(
                    name=name,
                    metadata={"description": f"Collection for {name}"}
                )
                self.collections[name] = collection
                print(f"✅ 集合创建成功: {name}")
            except Exception as e:
                print(f"❌ 集合创建失败 {name}: {e}")
    
    def _sanitize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """清理元数据，确保所有值都是ChromaDB支持的类型"""
        if not metadata:
            return {}

        sanitized = {}
        for key, value in metadata.items():
            if value is None:
                sanitized[key] = None
            elif isinstance(value, (str, int, float, bool)):
                sanitized[key] = value
            elif isinstance(value, list):
                # 将列表转换为逗号分隔的字符串
                sanitized[key] = ",".join(str(item) for item in value)
            else:
                # 其他类型转换为字符串
                sanitized[key] = str(value)

        return sanitized

    async def store_document(self, collection_name: str, doc_id: str,
                           content: str, metadata: Dict[str, Any] = None):
        """存储文档"""
        if not self.initialized:
            await self.initialize()

        try:
            collection = self.collections.get(collection_name)
            if not collection:
                print(f"❌ 集合不存在: {collection_name}")
                return False

            # 清理元数据
            clean_metadata = self._sanitize_metadata(metadata)

            collection.add(
                documents=[content],
                ids=[doc_id],
                metadatas=[clean_metadata]
            )

            return True
            
        except Exception as e:
            print(f"❌ 文档存储失败: {e}")
            return False
    
    async def search_documents(self, collection_name: str, query: str, 
                             limit: int = 5) -> List[Dict[str, Any]]:
        """搜索文档"""
        if not self.initialized:
            await self.initialize()
        
        try:
            collection = self.collections.get(collection_name)
            if not collection:
                return []
            
            results = collection.query(
                query_texts=[query],
                n_results=limit
            )
            
            # 格式化结果
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        'id': results['ids'][0][i],
                        'content': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'][0] else {},
                        'distance': results['distances'][0][i] if results['distances'] else None
                    }
                    formatted_results.append(result)
            
            return formatted_results
            
        except Exception as e:
            print(f"❌ 文档搜索失败: {e}")
            return []
    
    async def search_similar_requirements(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索相似需求"""
        return await self.search_documents("requirements", query, limit)

    async def search_similar_testcases(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索相似测试用例"""
        return await self.search_documents("testcases", query, limit)

    async def store_requirement(self, requirement) -> bool:
        """存储需求文档"""
        metadata = {
            "title": requirement.title,
            "created_by": requirement.created_by,
            "priority": requirement.priority.value if hasattr(requirement.priority, 'value') else str(requirement.priority),
            "tags": ",".join(requirement.tags) if requirement.tags else "",
            "created_at": requirement.created_at.isoformat() if hasattr(requirement.created_at, 'isoformat') else str(requirement.created_at),
            "updated_at": requirement.updated_at.isoformat() if hasattr(requirement.updated_at, 'isoformat') else str(requirement.updated_at)
        }

        content = f"标题: {requirement.title}\n内容: {requirement.content}"
        return await self.store_document("requirements", requirement.id, content, metadata)

    async def store_testcase(self, testcase) -> bool:
        """存储测试用例"""
        metadata = {
            "title": testcase.title,
            "test_type": testcase.test_type,  # 修复：使用test_type而不是type
            "priority": testcase.priority.value,
            "requirement_id": testcase.requirement_id,
            "created_at": testcase.created_at.isoformat()
        }

        # 构建测试用例内容
        preconditions_str = '; '.join(testcase.preconditions) if testcase.preconditions else '无'

        # 处理测试步骤
        if testcase.test_steps:
            steps_list = []
            for i, step in enumerate(testcase.test_steps):
                action = step.get("action", "") if isinstance(step, dict) else str(step)
                steps_list.append(f"{i+1}. {action}")
            steps_str = '; '.join(steps_list)
        else:
            steps_str = '无'

        expected_results_str = '; '.join(testcase.expected_results) if testcase.expected_results else '无'
        tags_str = ', '.join(testcase.tags) if testcase.tags else '无'

        content_parts = [
            f"标题: {testcase.title}",
            f"描述: {testcase.description}",
            f"测试类型: {testcase.test_type}",
            f"测试级别: {testcase.test_level}",
            f"前置条件: {preconditions_str}",
            f"测试步骤: {steps_str}",
            f"预期结果: {expected_results_str}",
            f"标签: {tags_str}"
        ]
        content = "\n".join(content_parts)

        return await self.store_document("testcases", testcase.id, content, metadata)

    async def store_review_result(self, review_result) -> bool:
        """存储评审结果"""
        metadata = {
            "reviewer_agent": review_result.reviewer_agent.value if hasattr(review_result.reviewer_agent, 'value') else str(review_result.reviewer_agent),
            "target_id": review_result.target_id,
            "target_type": review_result.target_type,
            "approved": review_result.approved,
            "score": review_result.score,
            "created_at": review_result.created_at.isoformat() if hasattr(review_result.created_at, 'isoformat') else str(review_result.created_at)
        }

        content = f"评审结果: {'通过' if review_result.approved else '不通过'}\n评分: {review_result.score}\n评审意见: {review_result.comments}\n改进建议: {review_result.suggestions}"
        return await self.store_document("reviews", review_result.id, content, metadata)

    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        if not self.initialized:
            await self.initialize()

        stats = {}
        for name, collection in self.collections.items():
            try:
                count = collection.count()
                stats[name] = {"count": count}
            except Exception as e:
                stats[name] = {"error": str(e)}

        return stats
    
    async def cleanup(self):
        """清理资源"""
        if self.client:
            # ChromaDB客户端会自动处理连接关闭
            pass
        self.initialized = False


# 创建全局实例
simple_chroma_manager = SimpleChromaDBManager()