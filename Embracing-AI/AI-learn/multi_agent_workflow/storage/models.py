"""
数据模型定义模块
"""
from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid


class WorkflowStatus(str, Enum):
    """工作流状态枚举"""
    PENDING = "pending"          # 待处理
    IN_PROGRESS = "in_progress"  # 进行中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"     # 已取消


class AgentType(str, Enum):
    """智能体类型枚举"""
    REQUIREMENT_ANALYZER = "requirement_analyzer"    # 需求分析智能体
    REQUIREMENT_REVIEWER = "requirement_reviewer"    # 需求评审智能体
    TESTCASE_GENERATOR = "testcase_generator"       # 测试用例生成智能体
    TESTCASE_REVIEWER = "testcase_reviewer"         # 用例评审智能体


class Priority(str, Enum):
    """优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RequirementDocument(BaseModel):
    """需求文档模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(..., description="需求标题")
    content: str = Field(..., description="需求内容")
    priority: Priority = Field(default=Priority.MEDIUM, description="优先级")
    tags: List[str] = Field(default_factory=list, description="标签")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    created_by: str = Field(..., description="创建者")
    
    # 需求分析结果
    analysis_result: Optional[Dict[str, Any]] = Field(default=None, description="分析结果")
    functional_requirements: List[str] = Field(default_factory=list, description="功能需求")
    non_functional_requirements: List[str] = Field(default_factory=list, description="非功能需求")
    acceptance_criteria: List[str] = Field(default_factory=list, description="验收标准")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ReviewResult(BaseModel):
    """评审结果模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    reviewer_agent: AgentType = Field(..., description="评审智能体类型")
    target_id: str = Field(..., description="评审目标ID")
    target_type: Literal["requirement", "testcase"] = Field(..., description="评审目标类型")
    
    # 评审结果
    approved: bool = Field(..., description="是否通过")
    score: float = Field(ge=0, le=100, description="评分(0-100)")
    comments: List[str] = Field(default_factory=list, description="评审意见")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    issues: List[str] = Field(default_factory=list, description="发现的问题")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now)
    review_duration: Optional[float] = Field(default=None, description="评审耗时(秒)")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TestCase(BaseModel):
    """测试用例模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    requirement_id: str = Field(..., description="关联需求ID")
    title: str = Field(..., description="测试用例标题")
    description: str = Field(..., description="测试用例描述")
    
    # 测试用例详情
    preconditions: List[str] = Field(default_factory=list, description="前置条件")
    test_steps: List[Dict[str, str]] = Field(default_factory=list, description="测试步骤")
    expected_results: List[str] = Field(default_factory=list, description="预期结果")
    test_data: Optional[Dict[str, Any]] = Field(default=None, description="测试数据")
    
    # 分类信息
    test_type: str = Field(..., description="测试类型(功能/性能/安全等)")
    test_level: str = Field(..., description="测试级别(单元/集成/系统等)")
    priority: Priority = Field(default=Priority.MEDIUM, description="优先级")
    tags: List[str] = Field(default_factory=list, description="标签")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    created_by_agent: AgentType = Field(..., description="创建智能体")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class WorkflowTask(BaseModel):
    """工作流任务模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    requirement_id: str = Field(..., description="关联需求ID")
    current_stage: AgentType = Field(..., description="当前阶段")
    status: WorkflowStatus = Field(default=WorkflowStatus.PENDING, description="任务状态")
    
    # 执行历史
    execution_history: List[Dict[str, Any]] = Field(default_factory=list, description="执行历史")
    current_data: Dict[str, Any] = Field(default_factory=dict, description="当前数据")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    
    # 错误信息
    error_message: Optional[str] = Field(default=None, description="错误信息")
    retry_count: int = Field(default=0, description="重试次数")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AgentMessage(BaseModel):
    """智能体消息模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    sender_agent: AgentType = Field(..., description="发送智能体")
    receiver_agent: Optional[AgentType] = Field(default=None, description="接收智能体")
    message_type: str = Field(..., description="消息类型")
    
    # 消息内容
    content: Dict[str, Any] = Field(..., description="消息内容")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    # 关联信息
    workflow_task_id: str = Field(..., description="关联工作流任务ID")
    requirement_id: str = Field(..., description="关联需求ID")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now)
    processed_at: Optional[datetime] = Field(default=None, description="处理时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SystemMetrics(BaseModel):
    """系统指标模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    
    # 性能指标
    active_workflows: int = Field(default=0, description="活跃工作流数量")
    completed_workflows: int = Field(default=0, description="已完成工作流数量")
    failed_workflows: int = Field(default=0, description="失败工作流数量")
    
    # 智能体指标
    agent_response_times: Dict[str, float] = Field(default_factory=dict, description="智能体响应时间")
    agent_success_rates: Dict[str, float] = Field(default_factory=dict, description="智能体成功率")
    
    # 资源使用
    memory_usage: float = Field(default=0.0, description="内存使用率")
    cpu_usage: float = Field(default=0.0, description="CPU使用率")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }