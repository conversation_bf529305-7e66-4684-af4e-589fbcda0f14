"""
ChromaDB数据持久化管理模块
"""
import asyncio
import json
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.settings import settings
from storage.models import (
    RequirementDocument, TestCase, ReviewResult, 
    WorkflowTask, AgentMessage, SystemMetrics
)


class ChromaDBManager:
    """ChromaDB管理器 - 处理向量存储和检索"""
    
    def __init__(self):
        self.client = None
        self.collections = {}
        self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name="all-MiniLM-L6-v2"
        )
        
    async def initialize(self):
        """初始化ChromaDB客户端和集合"""
        # 确保数据目录存在
        settings.ensure_data_dirs()
        
        # 创建ChromaDB客户端
        self.client = chromadb.PersistentClient(
            path=str(settings.chroma_db_full_path),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 创建各种集合
        await self._create_collections()
        
    async def _create_collections(self):
        """创建所需的集合"""
        collection_configs = {
            "requirements": {
                "name": "requirements",
                "metadata": {"description": "需求文档存储"}
            },
            "testcases": {
                "name": "testcases", 
                "metadata": {"description": "测试用例存储"}
            },
            "reviews": {
                "name": "reviews",
                "metadata": {"description": "评审结果存储"}
            },
            "workflows": {
                "name": "workflows",
                "metadata": {"description": "工作流任务存储"}
            },
            "messages": {
                "name": "agent_messages",
                "metadata": {"description": "智能体消息存储"}
            },
            "knowledge_base": {
                "name": "knowledge_base",
                "metadata": {"description": "知识库存储"}
            }
        }
        
        for key, config in collection_configs.items():
            try:
                collection = self.client.get_or_create_collection(
                    name=config["name"],
                    embedding_function=self.embedding_function,
                    metadata=config["metadata"]
                )
                self.collections[key] = collection
            except Exception as e:
                print(f"创建集合 {config['name']} 失败: {e}")
                raise
    
    async def store_requirement(self, requirement: RequirementDocument) -> str:
        """存储需求文档"""
        try:
            collection = self.collections["requirements"]
            
            # 准备文档内容用于向量化
            content_for_embedding = f"""
            标题: {requirement.title}
            内容: {requirement.content}
            功能需求: {' '.join(requirement.functional_requirements)}
            非功能需求: {' '.join(requirement.non_functional_requirements)}
            验收标准: {' '.join(requirement.acceptance_criteria)}
            标签: {' '.join(requirement.tags)}
            """
            
            # 准备元数据
            metadata = {
                "title": requirement.title,
                "priority": requirement.priority.value,
                "created_by": requirement.created_by,
                "created_at": requirement.created_at.isoformat(),
                "tags": json.dumps(requirement.tags),
                "type": "requirement"
            }
            
            # 存储到ChromaDB
            collection.add(
                documents=[content_for_embedding.strip()],
                metadatas=[metadata],
                ids=[requirement.id]
            )
            
            return requirement.id
            
        except Exception as e:
            print(f"存储需求文档失败: {e}")
            raise
    
    async def store_testcase(self, testcase: TestCase) -> str:
        """存储测试用例"""
        try:
            collection = self.collections["testcases"]
            
            # 准备测试用例内容用于向量化
            steps_text = " ".join([
                f"步骤{i+1}: {step.get('action', '')} 预期: {step.get('expected', '')}"
                for i, step in enumerate(testcase.test_steps)
            ])
            
            content_for_embedding = f"""
            标题: {testcase.title}
            描述: {testcase.description}
            前置条件: {' '.join(testcase.preconditions)}
            测试步骤: {steps_text}
            预期结果: {' '.join(testcase.expected_results)}
            测试类型: {testcase.test_type}
            测试级别: {testcase.test_level}
            标签: {' '.join(testcase.tags)}
            """
            
            # 准备元数据
            metadata = {
                "title": testcase.title,
                "requirement_id": testcase.requirement_id,
                "test_type": testcase.test_type,
                "test_level": testcase.test_level,
                "priority": testcase.priority.value,
                "created_by_agent": testcase.created_by_agent.value,
                "created_at": testcase.created_at.isoformat(),
                "tags": json.dumps(testcase.tags),
                "type": "testcase"
            }
            
            # 存储到ChromaDB
            collection.add(
                documents=[content_for_embedding.strip()],
                metadatas=[metadata],
                ids=[testcase.id]
            )
            
            return testcase.id
            
        except Exception as e:
            print(f"存储测试用例失败: {e}")
            raise
    
    async def store_review_result(self, review: ReviewResult) -> str:
        """存储评审结果"""
        try:
            collection = self.collections["reviews"]
            
            # 准备评审内容用于向量化
            content_for_embedding = f"""
            评审类型: {review.target_type}
            评审结果: {'通过' if review.approved else '不通过'}
            评分: {review.score}
            评审意见: {' '.join(review.comments)}
            改进建议: {' '.join(review.suggestions)}
            发现问题: {' '.join(review.issues)}
            """
            
            # 准备元数据
            metadata = {
                "reviewer_agent": review.reviewer_agent.value,
                "target_id": review.target_id,
                "target_type": review.target_type,
                "approved": review.approved,
                "score": review.score,
                "created_at": review.created_at.isoformat(),
                "type": "review"
            }
            
            # 存储到ChromaDB
            collection.add(
                documents=[content_for_embedding.strip()],
                metadatas=[metadata],
                ids=[review.id]
            )
            
            return review.id
            
        except Exception as e:
            print(f"存储评审结果失败: {e}")
            raise
    
    async def search_similar_requirements(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索相似需求"""
        try:
            collection = self.collections["requirements"]
            
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where={"type": "requirement"}
            )
            
            return self._format_search_results(results)
            
        except Exception as e:
            print(f"搜索相似需求失败: {e}")
            return []
    
    async def search_similar_testcases(self, query: str, requirement_id: Optional[str] = None, 
                                     limit: int = 5) -> List[Dict[str, Any]]:
        """搜索相似测试用例"""
        try:
            collection = self.collections["testcases"]
            
            where_clause = {"type": "testcase"}
            if requirement_id:
                where_clause["requirement_id"] = requirement_id
            
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_clause
            )
            
            return self._format_search_results(results)
            
        except Exception as e:
            print(f"搜索相似测试用例失败: {e}")
            return []
    
    async def get_requirement_by_id(self, requirement_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取需求"""
        try:
            collection = self.collections["requirements"]
            
            results = collection.get(
                ids=[requirement_id],
                include=["documents", "metadatas"]
            )
            
            if results["ids"]:
                return {
                    "id": results["ids"][0],
                    "document": results["documents"][0],
                    "metadata": results["metadatas"][0]
                }
            return None
            
        except Exception as e:
            print(f"获取需求失败: {e}")
            return None
    
    async def get_testcases_by_requirement(self, requirement_id: str) -> List[Dict[str, Any]]:
        """获取需求相关的所有测试用例"""
        try:
            collection = self.collections["testcases"]
            
            results = collection.get(
                where={"requirement_id": requirement_id},
                include=["documents", "metadatas"]
            )
            
            return [
                {
                    "id": id_,
                    "document": doc,
                    "metadata": meta
                }
                for id_, doc, meta in zip(results["ids"], results["documents"], results["metadatas"])
            ]
            
        except Exception as e:
            print(f"获取测试用例失败: {e}")
            return []
    
    async def store_knowledge(self, content: str, metadata: Dict[str, Any]) -> str:
        """存储知识库内容"""
        try:
            collection = self.collections["knowledge_base"]
            
            knowledge_id = f"knowledge_{datetime.now().timestamp()}"
            
            collection.add(
                documents=[content],
                metadatas=[metadata],
                ids=[knowledge_id]
            )
            
            return knowledge_id
            
        except Exception as e:
            print(f"存储知识库内容失败: {e}")
            raise
    
    async def search_knowledge(self, query: str, limit: int = 3) -> List[Dict[str, Any]]:
        """搜索知识库"""
        try:
            collection = self.collections["knowledge_base"]
            
            results = collection.query(
                query_texts=[query],
                n_results=limit
            )
            
            return self._format_search_results(results)
            
        except Exception as e:
            print(f"搜索知识库失败: {e}")
            return []
    
    def _format_search_results(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """格式化搜索结果"""
        formatted_results = []
        
        if not results["ids"] or not results["ids"][0]:
            return formatted_results
        
        for i in range(len(results["ids"][0])):
            result = {
                "id": results["ids"][0][i],
                "document": results["documents"][0][i],
                "metadata": results["metadatas"][0][i],
                "distance": results["distances"][0][i] if "distances" in results else None
            }
            formatted_results.append(result)
        
        return formatted_results
    
    async def cleanup(self):
        """清理资源"""
        if self.client:
            # ChromaDB客户端通常不需要显式关闭
            pass
    
    async def reset_collections(self):
        """重置所有集合（用于测试）"""
        if self.client:
            try:
                self.client.reset()
                await self._create_collections()
            except Exception as e:
                print(f"重置集合失败: {e}")
                raise
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        stats = {}
        try:
            for name, collection in self.collections.items():
                count = collection.count()
                stats[name] = {
                    "count": count,
                    "name": collection.name
                }
        except Exception as e:
            print(f"获取集合统计失败: {e}")
        
        return stats


# 全局ChromaDB管理器实例
chroma_manager = ChromaDBManager()
