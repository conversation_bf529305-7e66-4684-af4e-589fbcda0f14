# 异步多智能体需求测试工作流系统

基于Python+asyncio和AutoGen框架的多智能体协作系统，实现从需求分析到测试用例生成的完整自动化流程，集成了智能体长久记忆功能。

## 🎯 系统特性

### 核心智能体
- **需求分析智能体**: 解析和结构化用户需求，提取关键信息
- **需求评审智能体**: 评估需求完整性和可行性，提供改进建议
- **测试用例生成智能体**: 基于需求自动生成全面的测试用例
- **用例评审智能体**: 评审测试用例质量和覆盖度

### 技术架构
- **本地模型支持**: 优先支持Ollama部署的DeepSeek-R1:1.5b模型
- **异步处理**: 基于asyncio的高性能异步架构
- **事件驱动**: 智能体间通过事件进行松耦合通信
- **向量存储**: ChromaDB持久化需求和测试用例数据
- **智能记忆**: AutoGen Memory模块保持智能体长久记忆
- **工作流编排**: 自动化管理多智能体协作流程
- **模型适配**: 支持Ollama、OpenAI、Azure OpenAI多种模型提供商

### 协作模式
- **反思模式**: 智能体间的评审和改进循环
- **交接模式**: 任务在智能体间的流转传递
- **并发处理**: 提升系统整体执行效率

## 📁 项目结构
```
multi_agent_workflow/
├── README.md                   # 项目说明文档
├── requirements.txt            # 依赖包列表
├── .env.example               # 环境变量模板
├── main.py                    # 主程序入口
├── example.py                 # 使用示例
├── config/                    # 配置管理
│   └── settings.py           # 系统配置
├── core/                      # 核心基础设施
│   ├── events.py             # 事件系统
│   └── memory_manager.py     # 记忆管理
├── agents/                    # 智能体实现
│   ├── requirement_analyzer.py    # 需求分析智能体
│   ├── requirement_reviewer.py    # 需求评审智能体
│   ├── testcase_generator.py     # 测试用例生成智能体
│   └── testcase_reviewer.py      # 用例评审智能体
├── storage/                   # 数据持久化
│   ├── models.py             # 数据模型
│   └── chroma_manager.py     # ChromaDB管理
└── workflow/                  # 工作流引擎
    └── orchestrator.py       # 工作流编排器
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd multi_agent_workflow

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置必要参数
vim .env
```

**配置选项**:

**使用Ollama本地模型 (推荐)**:
```bash
# 模型提供商
MODEL_PROVIDER=ollama

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=deepseek-r1:1.5b

# ChromaDB配置
CHROMA_DB_PATH=./data/chroma_db
CHROMA_COLLECTION_NAME=workflow_memory
```

**使用OpenAI (备用)**:
```bash
# 模型提供商
MODEL_PROVIDER=openai

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
```

### 3. 运行系统

#### 演示模式
```bash
python main.py demo
```

#### 交互模式
```bash
python main.py
```

#### 使用示例
```bash
python example.py
```

## 💡 使用示例

### 基础使用

```python
import asyncio
from main import MultiAgentWorkflowSystem
from storage.models import Priority

async def basic_example():
    system = MultiAgentWorkflowSystem()
    
    try:
        result = await system.process_requirement(
            title="用户登录功能",
            content="""
            实现用户登录功能，包括：
            1. 用户名密码验证
            2. 记住登录状态
            3. 错误提示
            4. 密码找回
            """,
            priority=Priority.HIGH,
            tags=["用户管理", "安全"]
        )
        
        print(f"处理结果: {result}")
        
    finally:
        await system.cleanup()

# 运行示例
asyncio.run(basic_example())
```

### 批量处理

```python
async def batch_example():
    system = MultiAgentWorkflowSystem()
    
    requirements = [
        {
            "title": "订单管理",
            "content": "实现订单CRUD操作",
            "priority": Priority.HIGH
        },
        {
            "title": "支付集成", 
            "content": "集成第三方支付",
            "priority": Priority.CRITICAL
        }
    ]
    
    try:
        for req in requirements:
            result = await system.process_requirement(**req)
            print(f"完成: {req['title']}")
    finally:
        await system.cleanup()
```

## 🔧 核心功能详解

### 智能体记忆系统

系统集成了AutoGen的Memory模块，为每个智能体提供长久记忆能力：

- **专用记忆**: 每个智能体维护专门的经验记忆
- **共享记忆**: 智能体间共享的知识库
- **向量检索**: 基于语义相似度的记忆检索
- **经验积累**: 自动存储和学习历史经验

### 工作流编排

自动化的多阶段工作流程：

1. **需求分析阶段**: 解析需求，提取关键信息
2. **需求评审阶段**: 评估质量，提供改进建议
3. **用例生成阶段**: 生成全面的测试用例
4. **用例评审阶段**: 评审用例质量和覆盖度

### 事件驱动架构

- **异步通信**: 智能体间通过事件异步通信
- **松耦合设计**: 智能体独立运行，通过事件协调
- **实时监控**: 完整的事件日志和状态追踪

## 📊 系统监控

### 获取系统状态

```python
async def check_status():
    system = MultiAgentWorkflowSystem()
    await system.initialize()
    
    status = await system.get_system_status()
    print("系统状态:", status)
```

### 工作流监控

```python
# 获取活跃工作流
active_workflows = await orchestrator.get_active_workflows()

# 获取工作流统计
stats = await orchestrator.get_workflow_statistics()

# 获取记忆统计
memory_stats = await global_memory_manager.get_memory_statistics()
```

## 🎨 自定义扩展

### 添加新智能体

```python
from agents.base import BaseAgent

class CustomAgent(BaseAgent):
    def __init__(self, model_client):
        super().__init__(AgentType.CUSTOM, model_client)
    
    async def process_task(self, task_data):
        # 实现自定义逻辑
        pass
```

### 自定义工作流

```python
from workflow.orchestrator import WorkflowOrchestrator

class CustomOrchestrator(WorkflowOrchestrator):
    async def custom_workflow(self, data):
        # 实现自定义工作流
        pass
```

## 🔍 故障排除

### 常见问题

1. **API连接失败**
   ```bash
   # 检查API密钥配置
   echo $OPENAI_API_KEY
   
   # 测试网络连接
   curl -I https://api.openai.com
   ```

2. **ChromaDB初始化失败**
   ```bash
   # 检查数据目录权限
   ls -la ./data/
   
   # 清理数据目录
   rm -rf ./data/chroma_db
   ```

3. **内存不足**
   ```bash
   # 调整配置参数
   MAX_CONCURRENT_AGENTS=2
   WORKFLOW_TIMEOUT=180
   ```

### 调试模式

```bash
# 启用详细日志
LOG_LEVEL=DEBUG python main.py

# 查看事件日志
tail -f logs/events.log
```

## 📈 性能优化

### 配置优化

```bash
# 调整并发数
MAX_CONCURRENT_AGENTS=4

# 设置超时时间
WORKFLOW_TIMEOUT=300

# 优化记忆检索
MEMORY_SEARCH_LIMIT=5
```

### 资源管理

```python
# 定期清理记忆
await global_memory_manager.cleanup_old_memories()

# 监控资源使用
stats = await system.get_system_status()
print(f"内存使用: {stats['memory_usage']}")
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🔗 相关资源

- [AutoGen 官方文档](https://microsoft.github.io/autogen/)
- [ChromaDB 文档](https://docs.trychroma.com/)
- [AsyncIO 教程](https://docs.python.org/3/library/asyncio.html)

## 📞 支持与反馈

如有问题或建议，欢迎通过以下方式联系：

- 提交 Issue
- 发起 Discussion
- 邮件联系

---

🚀 **开始你的智能化需求测试之旅！**