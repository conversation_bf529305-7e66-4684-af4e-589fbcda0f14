"""
需求分析智能体 - 负责解析和结构化用户需求
"""
import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from storage.models import RequirementDocument, AgentType, Priority
from storage.simple_chroma_manager import simple_chroma_manager as chroma_manager
from core.events import global_event_bus, AgentTaskEvent, RequirementEvent
from core.memory_manager import global_memory_manager
from core.agent_adapter import create_agent_adapter
from config.settings import settings


class RequirementAnalyzer:
    """需求分析智能体"""
    
    def __init__(self, model_client):
        self.model_client = model_client
        self.agent_type = AgentType.REQUIREMENT_ANALYZER
        
        # 初始化记忆管理器
        self.memory_manager = global_memory_manager
        
        # 创建智能体适配器
        self.agent = create_agent_adapter(
            name="requirement_analyzer",
            model_client=model_client,
            system_message=self._get_system_message()
        )
    
    def _get_system_message(self) -> str:
        """获取系统提示消息"""
        return """
你是一个专业的需求分析师，负责分析和结构化用户需求。你的任务包括：

1. **需求理解**：深入理解用户提供的需求描述
2. **需求分类**：将需求分为功能需求和非功能需求
3. **需求细化**：提取关键信息和业务逻辑
4. **验收标准**：定义清晰的验收标准
5. **优先级评估**：评估需求的优先级

**分析输出格式**：
请以JSON格式输出分析结果，包含以下字段：
- functional_requirements: 功能需求列表
- non_functional_requirements: 非功能需求列表  
- acceptance_criteria: 验收标准列表
- priority: 优先级(low/medium/high/critical)
- tags: 相关标签列表
- analysis_summary: 分析总结
- potential_risks: 潜在风险
- dependencies: 依赖关系

**分析原则**：
- 保持客观和专业
- 关注需求的完整性和一致性
- 识别模糊或不完整的需求
- 考虑技术可行性和业务价值

请确保输出的JSON格式正确，可以被程序解析。
"""
    
    async def analyze_requirement(self, requirement: RequirementDocument, 
                                workflow_task_id: str) -> Dict[str, Any]:
        """分析需求文档"""
        start_time = time.time()
        
        # 发布任务开始事件
        await global_event_bus.publish(
            AgentTaskEvent.task_started(
                agent_type=self.agent_type,
                task_description=f"分析需求: {requirement.title}",
                workflow_task_id=workflow_task_id
            )
        )
        
        try:
            # 搜索相似需求作为参考
            similar_requirements = await chroma_manager.search_similar_requirements(
                query=f"{requirement.title} {requirement.content}",
                limit=3
            )
            
            # 获取相关记忆
            memory_context = await self.memory_manager.get_relevant_memories(
                self.agent_type, 
                f"{requirement.title} {requirement.content}"
            )
            
            # 构建分析提示
            analysis_prompt = self._build_analysis_prompt(requirement, similar_requirements, memory_context)
            
            # 调用智能体进行分析
            response = await self.agent.run(
                task=analysis_prompt,
                temperature=settings.requirement_analyzer_temperature
            )
            
            # 解析分析结果
            analysis_result = self._parse_analysis_result(response)
            
            # 更新需求文档
            requirement.analysis_result = analysis_result
            requirement.functional_requirements = analysis_result.get("functional_requirements", [])
            requirement.non_functional_requirements = analysis_result.get("non_functional_requirements", [])
            requirement.acceptance_criteria = analysis_result.get("acceptance_criteria", [])
            
            # 更新优先级
            priority_str = analysis_result.get("priority", "medium")
            try:
                requirement.priority = Priority(priority_str.lower())
            except ValueError:
                requirement.priority = Priority.MEDIUM
            
            # 更新标签
            if analysis_result.get("tags"):
                # 确保tags是列表格式
                new_tags = analysis_result["tags"]
                if isinstance(new_tags, list):
                    requirement.tags.extend(new_tags)
                else:
                    requirement.tags.append(str(new_tags))
            
            requirement.updated_at = datetime.now()
            
            # 存储到ChromaDB
            await chroma_manager.store_requirement(requirement)
            
            # 存储分析经验到记忆
            await self.memory_manager.store_requirement_memory(requirement, analysis_result)
            
            execution_time = time.time() - start_time
            
            # 发布任务完成事件
            await global_event_bus.publish(
                AgentTaskEvent.task_completed(
                    agent_type=self.agent_type,
                    task_description=f"分析需求: {requirement.title}",
                    workflow_task_id=workflow_task_id,
                    execution_time=execution_time,
                    result=analysis_result
                )
            )
            
            # 发布需求分析完成事件
            await global_event_bus.publish(
                RequirementEvent.requirement_analyzed(
                    requirement_id=requirement.id,
                    workflow_task_id=workflow_task_id,
                    analysis_result=analysis_result
                )
            )
            
            return analysis_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 发布任务失败事件
            await global_event_bus.publish(
                AgentTaskEvent(
                    id="",
                    event_type="agent_task_failed",
                    timestamp=datetime.now(),
                    source_agent=self.agent_type,
                    workflow_task_id=workflow_task_id,
                    agent_type=self.agent_type,
                    task_description=f"分析需求: {requirement.title}",
                    execution_time=execution_time,
                    data={"error": str(e)}
                )
            )
            
            raise Exception(f"需求分析失败: {str(e)}")
    
    def _build_analysis_prompt(self, requirement: RequirementDocument, 
                             similar_requirements: List[Dict[str, Any]],
                             memory_context: str = "") -> str:
        """构建分析提示"""
        prompt = f"""
请分析以下需求文档：

**需求标题**: {requirement.title}
**需求内容**: 
{requirement.content}

**创建者**: {requirement.created_by}
**当前标签**: {', '.join(requirement.tags) if requirement.tags else '无'}
"""
        
        if similar_requirements:
            prompt += "\n**相似需求参考**:\n"
            for i, similar in enumerate(similar_requirements, 1):
                prompt += f"{i}. {similar['metadata'].get('title', '未知标题')}\n"
        
        if memory_context:
            prompt += f"\n{memory_context}\n"
        
        prompt += """
请根据上述信息和相关经验进行深入分析，并以JSON格式返回分析结果。
确保分析结果完整、准确、可操作。

输出示例：
{
  "functional_requirements": ["功能需求1", "功能需求2"],
  "non_functional_requirements": ["非功能需求1", "非功能需求2"],
  "acceptance_criteria": ["验收标准1", "验收标准2"],
  "priority": "high",
  "tags": ["标签1", "标签2"],
  "analysis_summary": "需求分析总结",
  "potential_risks": ["风险1", "风险2"],
  "dependencies": ["依赖1", "依赖2"]
}
"""
        
        return prompt
    
    def _parse_analysis_result(self, response) -> Dict[str, Any]:
        """解析分析结果"""
        try:
            # 从响应中提取JSON内容
            response_text = str(response).strip()
            
            # 尝试找到JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证和补充必要字段
                default_result = {
                    "functional_requirements": [],
                    "non_functional_requirements": [],
                    "acceptance_criteria": [],
                    "priority": "medium",
                    "tags": [],
                    "analysis_summary": "",
                    "potential_risks": [],
                    "dependencies": []
                }
                
                # 合并结果，确保所有字段都存在
                for key, default_value in default_result.items():
                    if key not in result:
                        result[key] = default_value
                    elif not result[key] and isinstance(default_value, list):
                        result[key] = default_value
                
                return result
            else:
                # 如果无法解析JSON，创建默认结果
                return self._create_default_analysis_result(response_text)
                
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            # JSON解析失败，创建默认结果
            return self._create_default_analysis_result(str(response))
        except Exception as e:
            print(f"分析结果解析异常: {e}")
            return self._create_default_analysis_result(str(response))
    
    def _create_default_analysis_result(self, response_text: str) -> Dict[str, Any]:
        """创建默认分析结果"""
        return {
            "functional_requirements": ["需要进一步明确功能需求"],
            "non_functional_requirements": ["需要进一步明确非功能需求"],
            "acceptance_criteria": ["需要定义具体的验收标准"],
            "priority": "medium",
            "tags": ["待分析"],
            "analysis_summary": f"自动分析结果: {response_text[:200]}..." if response_text else "分析结果为空",
            "potential_risks": ["需求分析不完整"],
            "dependencies": []
        }
    
    async def get_analysis_capabilities(self) -> Dict[str, Any]:
        """获取分析能力描述"""
        return {
            "agent_type": self.agent_type.value,
            "capabilities": [
                "需求理解和解析",
                "功能需求提取",
                "非功能需求识别",
                "验收标准定义",
                "优先级评估",
                "风险识别",
                "依赖关系分析"
            ],
            "supported_formats": ["文本", "结构化文档"],
            "output_format": "JSON结构化数据",
            "model_info": {
                "provider": settings.model_provider,
                "model": settings.ollama_model if settings.is_using_ollama else settings.openai_model,
                "temperature": settings.requirement_analyzer_temperature
            }
        }