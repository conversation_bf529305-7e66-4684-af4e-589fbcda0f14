"""
测试用例生成智能体 - 基于需求生成全面的测试用例
"""
import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import time

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from storage.models import RequirementDocument, TestCase, AgentType, Priority
from storage.simple_chroma_manager import simple_chroma_manager as chroma_manager
from core.events import global_event_bus, AgentTaskEvent, TestCaseEvent
from core.memory_manager import global_memory_manager
from config.settings import settings


class TestCaseGenerator:
    """测试用例生成智能体"""
    
    def __init__(self, model_client):
        self.model_client = model_client
        self.agent_type = AgentType.TESTCASE_GENERATOR
        
        # 初始化记忆管理器
        self.memory_manager = global_memory_manager
        
        # 创建AutoGen智能体
        self.agent = AssistantAgent(
            name="testcase_generator",
            model_client=model_client,
            system_message=self._get_system_message(),
            tools=[],
            memory=[]
        )
    
    def _get_system_message(self) -> str:
        """获取系统提示消息"""
        return """
你是一个专业的测试用例设计专家，负责基于需求文档生成全面的测试用例。你的任务包括：

1. **测试场景分析**：分析需求中的各种测试场景
2. **用例设计**：设计覆盖正常、边界、异常情况的测试用例
3. **测试数据准备**：为测试用例准备合适的测试数据
4. **优先级分配**：根据需求重要性分配测试用例优先级
5. **分类标记**：对测试用例进行合理分类

**测试用例设计原则**：
- 功能覆盖：确保所有功能需求都有对应测试用例
- 边界测试：包含边界值和临界条件测试
- 异常处理：验证错误处理和异常情况
- 用户体验：关注用户使用场景和体验
- 性能考虑：包含必要的性能测试用例

**测试用例输出格式**：
请以JSON数组格式输出测试用例，每个测试用例包含以下字段：
- title: 测试用例标题
- description: 测试用例描述
- test_type: 测试类型(功能/性能/安全/兼容性等)
- test_level: 测试级别(单元/集成/系统/验收)
- priority: 优先级(low/medium/high/critical)
- preconditions: 前置条件列表
- test_steps: 测试步骤列表，每步包含action和expected
- expected_results: 预期结果列表
- test_data: 测试数据(可选)
- tags: 标签列表

**测试类型说明**：
- 功能测试：验证功能是否按需求实现
- 界面测试：验证用户界面和交互
- 性能测试：验证系统性能指标
- 安全测试：验证安全性和权限控制
- 兼容性测试：验证不同环境下的兼容性
- 易用性测试：验证用户体验和易用性
"""
    
    async def generate_testcases(self, requirement: RequirementDocument,
                                workflow_task_id: str) -> List[TestCase]:
        """基于需求生成测试用例"""
        start_time = time.time()
        
        # 发布任务开始事件
        await global_event_bus.publish(
            AgentTaskEvent.task_started(
                agent_type=self.agent_type,
                task_description=f"生成测试用例: {requirement.title}",
                workflow_task_id=workflow_task_id
            )
        )
        
        try:
            # 搜索相似测试用例作为参考
            similar_testcases = await chroma_manager.search_similar_testcases(
                query=f"{requirement.title} {requirement.content}",
                limit=3
            )
            
            # 获取相关记忆
            memory_context = await self.memory_manager.get_relevant_memories(
                self.agent_type,
                f"测试用例生成 {requirement.title} {requirement.content}"
            )
            
            # 构建生成提示
            generation_prompt = self._build_generation_prompt(requirement, similar_testcases, memory_context)
            
            # 调用智能体生成测试用例
            from autogen_agentchat.messages import TextMessage

            # 使用正确的AutoGen方式调用智能体
            stream = self.agent.run_stream(task=generation_prompt)
            response_content = ""
            async for message in stream:
                if hasattr(message, 'content'):
                    response_content += str(message.content)
                else:
                    response_content += str(message)

            response = response_content
            
            # 解析生成结果
            testcase_data_list = self._parse_generation_result(response)
            
            # 创建测试用例对象
            testcases = []
            for testcase_data in testcase_data_list:
                testcase = TestCase(
                    requirement_id=requirement.id,
                    title=testcase_data.get("title", "未命名测试用例"),
                    description=testcase_data.get("description", ""),
                    test_type=testcase_data.get("test_type", "功能测试"),
                    test_level=testcase_data.get("test_level", "系统测试"),
                    priority=self._parse_priority(testcase_data.get("priority", "medium")),
                    preconditions=testcase_data.get("preconditions", []),
                    test_steps=testcase_data.get("test_steps", []),
                    expected_results=testcase_data.get("expected_results", []),
                    test_data=testcase_data.get("test_data"),
                    tags=testcase_data.get("tags", []),
                    created_by_agent=self.agent_type
                )
                testcases.append(testcase)
            
            # 批量存储测试用例
            for testcase in testcases:
                await chroma_manager.store_testcase(testcase)
                # 存储测试用例设计经验到记忆
                await self.memory_manager.store_testcase_memory(testcase, requirement)
            
            execution_time = time.time() - start_time
            
            # 发布任务完成事件
            await global_event_bus.publish(
                AgentTaskEvent.task_completed(
                    agent_type=self.agent_type,
                    task_description=f"生成测试用例: {requirement.title}",
                    workflow_task_id=workflow_task_id,
                    execution_time=execution_time,
                    result={
                        "testcase_count": len(testcases),
                        "testcase_ids": [tc.id for tc in testcases]
                    }
                )
            )
            
            # 发布测试用例生成事件
            if testcases:
                await global_event_bus.publish(
                    TestCaseEvent.testcase_generated(
                        testcase_id=testcases[0].id,  # 使用第一个测试用例ID作为代表
                        requirement_id=requirement.id,
                        workflow_task_id=workflow_task_id,
                        testcase_count=len(testcases)
                    )
                )
            
            return testcases
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 发布任务失败事件
            await global_event_bus.publish(
                AgentTaskEvent(
                    id="",
                    event_type="agent_task_failed",
                    timestamp=datetime.now(),
                    source_agent=self.agent_type,
                    workflow_task_id=workflow_task_id,
                    agent_type=self.agent_type,
                    task_description=f"生成测试用例: {requirement.title}",
                    execution_time=execution_time,
                    data={"error": str(e)}
                )
            )
            
            raise Exception(f"测试用例生成失败: {str(e)}")
    
    def _build_generation_prompt(self, requirement: RequirementDocument,
                               similar_testcases: List[Dict[str, Any]],
                               memory_context: str = "") -> str:
        """构建生成提示"""
        prompt = f"""
请基于以下需求文档生成全面的测试用例：

**需求基本信息**:
- 标题: {requirement.title}
- 优先级: {requirement.priority.value}
- 标签: {', '.join(requirement.tags) if requirement.tags else '无'}

**需求内容**:
{requirement.content}

**功能需求** ({len(requirement.functional_requirements)}项):
"""
        
        for i, req in enumerate(requirement.functional_requirements, 1):
            prompt += f"{i}. {req}\n"
        
        prompt += f"\n**非功能需求** ({len(requirement.non_functional_requirements)}项):\n"
        for i, req in enumerate(requirement.non_functional_requirements, 1):
            prompt += f"{i}. {req}\n"
        
        prompt += f"\n**验收标准** ({len(requirement.acceptance_criteria)}项):\n"
        for i, criteria in enumerate(requirement.acceptance_criteria, 1):
            prompt += f"{i}. {criteria}\n"
        
        if similar_testcases:
            prompt += "\n**相似测试用例参考**:\n"
            for i, similar in enumerate(similar_testcases, 1):
                prompt += f"{i}. {similar['metadata'].get('title', '未知标题')} - {similar['metadata'].get('test_type', '未知类型')}\n"
        
        if memory_context:
            prompt += f"\n{memory_context}\n"
        
        prompt += """
请结合相关经验生成至少5个测试用例，确保覆盖：
1. 正常功能流程测试
2. 边界值和临界条件测试
3. 异常情况和错误处理测试
4. 用户体验和界面测试
5. 性能和安全相关测试（如适用）

请以JSON数组格式返回测试用例，确保每个测试用例都完整且可执行。
"""
        
        return prompt
    
    def _parse_generation_result(self, response) -> List[Dict[str, Any]]:
        """解析生成结果"""
        try:
            # 从响应中提取JSON内容
            response_text = str(response)
            
            # 尝试找到JSON数组
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证结果是列表
                if isinstance(result, list):
                    # 验证每个测试用例的必要字段
                    validated_testcases = []
                    for testcase in result:
                        if isinstance(testcase, dict):
                            validated_testcase = self._validate_testcase_data(testcase)
                            validated_testcases.append(validated_testcase)
                    
                    return validated_testcases if validated_testcases else [self._create_default_testcase()]
                else:
                    return [self._create_default_testcase()]
            else:
                # 如果无法解析JSON，创建默认测试用例
                return [self._create_default_testcase()]
                
        except json.JSONDecodeError:
            # JSON解析失败，创建默认测试用例
            return [self._create_default_testcase()]
    
    def _validate_testcase_data(self, testcase_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证和标准化测试用例数据"""
        validated = {
            "title": testcase_data.get("title", "未命名测试用例"),
            "description": testcase_data.get("description", ""),
            "test_type": testcase_data.get("test_type", "功能测试"),
            "test_level": testcase_data.get("test_level", "系统测试"),
            "priority": testcase_data.get("priority", "medium"),
            "preconditions": testcase_data.get("preconditions", []),
            "test_steps": testcase_data.get("test_steps", []),
            "expected_results": testcase_data.get("expected_results", []),
            "test_data": testcase_data.get("test_data"),
            "tags": testcase_data.get("tags", [])
        }
        
        # 确保列表字段是列表类型
        for field in ["preconditions", "test_steps", "expected_results", "tags"]:
            if not isinstance(validated[field], list):
                validated[field] = []
        
        # 验证测试步骤格式
        if validated["test_steps"]:
            formatted_steps = []
            for step in validated["test_steps"]:
                if isinstance(step, dict):
                    formatted_steps.append(step)
                elif isinstance(step, str):
                    formatted_steps.append({"action": step, "expected": ""})
            validated["test_steps"] = formatted_steps
        
        return validated
    
    def _create_default_testcase(self) -> Dict[str, Any]:
        """创建默认测试用例"""
        return {
            "title": "基础功能验证测试",
            "description": "验证基本功能是否正常工作",
            "test_type": "功能测试",
            "test_level": "系统测试",
            "priority": "medium",
            "preconditions": ["系统正常启动", "用户已登录"],
            "test_steps": [
                {"action": "执行基本功能操作", "expected": "功能正常响应"},
                {"action": "验证结果正确性", "expected": "结果符合预期"}
            ],
            "expected_results": ["功能执行成功", "结果正确显示"],
            "test_data": None,
            "tags": ["基础测试", "自动生成"]
        }
    
    def _parse_priority(self, priority_str: str) -> Priority:
        """解析优先级字符串"""
        try:
            return Priority(priority_str.lower())
        except ValueError:
            return Priority.MEDIUM
    
    async def generate_testcases_by_type(self, requirement: RequirementDocument,
                                       test_types: List[str],
                                       workflow_task_id: str) -> List[TestCase]:
        """按指定类型生成测试用例"""
        all_testcases = []
        
        for test_type in test_types:
            # 为每种测试类型生成专门的提示
            specialized_prompt = self._build_specialized_prompt(requirement, test_type)
            
            try:
                response = await self.agent.run(task=specialized_prompt)
                testcase_data_list = self._parse_generation_result(response)
                
                for testcase_data in testcase_data_list:
                    testcase_data["test_type"] = test_type  # 确保类型正确
                    testcase = TestCase(
                        requirement_id=requirement.id,
                        title=testcase_data.get("title", f"{test_type}测试用例"),
                        description=testcase_data.get("description", ""),
                        test_type=test_type,
                        test_level=testcase_data.get("test_level", "系统测试"),
                        priority=self._parse_priority(testcase_data.get("priority", "medium")),
                        preconditions=testcase_data.get("preconditions", []),
                        test_steps=testcase_data.get("test_steps", []),
                        expected_results=testcase_data.get("expected_results", []),
                        test_data=testcase_data.get("test_data"),
                        tags=testcase_data.get("tags", [test_type]),
                        created_by_agent=self.agent_type
                    )
                    all_testcases.append(testcase)
                    
            except Exception as e:
                print(f"生成{test_type}测试用例失败: {e}")
        
        # 存储所有测试用例
        for testcase in all_testcases:
            await chroma_manager.store_testcase(testcase)
        
        return all_testcases
    
    def _build_specialized_prompt(self, requirement: RequirementDocument, test_type: str) -> str:
        """构建专门化的生成提示"""
        base_prompt = f"""
请专门为"{test_type}"生成测试用例，基于以下需求：

**需求**: {requirement.title}
**内容**: {requirement.content}
"""
        
        type_specific_guidance = {
            "性能测试": "关注响应时间、吞吐量、资源使用率等性能指标",
            "安全测试": "关注权限控制、数据安全、输入验证等安全方面",
            "兼容性测试": "关注不同浏览器、操作系统、设备的兼容性",
            "易用性测试": "关注用户体验、界面友好性、操作便捷性",
            "接口测试": "关注API接口的功能、参数、返回值验证"
        }
        
        if test_type in type_specific_guidance:
            base_prompt += f"\n**{test_type}重点**: {type_specific_guidance[test_type]}\n"
        
        base_prompt += "请生成2-3个针对性的测试用例，以JSON数组格式返回。"
        
        return base_prompt
    
    async def get_generation_capabilities(self) -> Dict[str, Any]:
        """获取生成能力描述"""
        return {
            "agent_type": self.agent_type.value,
            "capabilities": [
                "功能测试用例生成",
                "性能测试用例设计",
                "安全测试用例创建",
                "兼容性测试用例规划",
                "边界值测试用例设计",
                "异常场景测试用例生成",
                "用户体验测试用例创建"
            ],
            "supported_test_types": [
                "功能测试", "性能测试", "安全测试", "兼容性测试",
                "易用性测试", "接口测试", "集成测试", "回归测试"
            ],
            "output_format": "结构化测试用例集合"
        }