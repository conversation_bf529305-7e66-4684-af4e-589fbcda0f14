"""
需求评审智能体 - 负责评估需求完整性和可行性
"""
import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from storage.models import RequirementDocument, ReviewResult, AgentType
from storage.simple_chroma_manager import simple_chroma_manager as chroma_manager
from core.events import global_event_bus, AgentTaskEvent
from core.memory_manager import global_memory_manager
from core.agent_adapter import create_agent_adapter
from config.settings import settings


class RequirementReviewer:
    """需求评审智能体"""
    
    def __init__(self, model_client):
        self.model_client = model_client
        self.agent_type = AgentType.REQUIREMENT_REVIEWER
        
        # 初始化记忆管理器
        self.memory_manager = global_memory_manager
        
        # 创建智能体适配器
        self.agent = create_agent_adapter(
            name="requirement_reviewer",
            model_client=model_client,
            system_message=self._get_system_message()
        )
    
    def _get_system_message(self) -> str:
        """获取系统提示消息"""
        return """
你是一个资深的需求评审专家，负责评估需求文档的质量和可行性。你的职责包括：

1. **完整性检查**：确保需求描述完整、清晰
2. **一致性验证**：检查需求内部逻辑一致性
3. **可行性评估**：评估技术和业务可行性
4. **质量评分**：给出客观的质量评分(0-100分)
5. **改进建议**：提供具体的改进建议

**评审标准**：
- 需求完整性 (25分)：描述是否完整、清晰
- 需求一致性 (25分)：内部逻辑是否一致
- 技术可行性 (25分)：技术实现是否可行
- 业务价值 (25分)：业务价值是否明确

**评审输出格式**：
请以JSON格式输出评审结果，包含以下字段：
- approved: 是否通过评审 (boolean)
- score: 总体评分 (0-100)
- comments: 评审意见列表
- suggestions: 改进建议列表
- issues: 发现的问题列表
- strengths: 需求优点列表

**评审原则**：
- 客观公正，基于质量标准
- 提供建设性的改进建议
- 关注实际可操作性
- 考虑业务价值和技术约束

请确保输出的JSON格式正确。
"""
    
    async def review_requirement(self, requirement: RequirementDocument, 
                               workflow_task_id: str) -> ReviewResult:
        """评审需求文档"""
        start_time = time.time()
        
        # 发布任务开始事件
        await global_event_bus.publish(
            AgentTaskEvent.task_started(
                agent_type=self.agent_type,
                task_description=f"评审需求: {requirement.title}",
                workflow_task_id=workflow_task_id
            )
        )
        
        try:
            # 获取相关记忆
            memory_context = await self.memory_manager.get_relevant_memories(
                self.agent_type,
                f"需求评审 {requirement.title} {requirement.content}"
            )
            
            # 构建评审提示
            review_prompt = self._build_review_prompt(requirement, memory_context)
            
            # 调用智能体进行评审
            response = await self.agent.run(
                task=review_prompt,
                temperature=settings.requirement_reviewer_temperature
            )
            
            # 解析评审结果
            review_data = self._parse_review_result(response)
            
            # 创建评审结果对象
            review_result = ReviewResult(
                reviewer_agent=self.agent_type,
                target_id=requirement.id,
                target_type="requirement",
                approved=review_data.get("approved", False),
                score=review_data.get("score", 0),
                comments=review_data.get("comments", []),
                suggestions=review_data.get("suggestions", []),
                issues=review_data.get("issues", []),
                review_duration=time.time() - start_time
            )
            
            # 存储评审结果
            await chroma_manager.store_review_result(review_result)
            
            # 存储评审经验到记忆
            await self.memory_manager.store_review_memory(
                review_result, 
                f"{requirement.title}: {requirement.content}"
            )
            
            execution_time = time.time() - start_time
            
            # 发布任务完成事件
            await global_event_bus.publish(
                AgentTaskEvent.task_completed(
                    agent_type=self.agent_type,
                    task_description=f"评审需求: {requirement.title}",
                    workflow_task_id=workflow_task_id,
                    execution_time=execution_time,
                    result={
                        "approved": review_result.approved,
                        "score": review_result.score,
                        "review_id": review_result.id
                    }
                )
            )
            
            return review_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 发布任务失败事件
            await global_event_bus.publish(
                AgentTaskEvent(
                    id="",
                    event_type="agent_task_failed",
                    timestamp=datetime.now(),
                    source_agent=self.agent_type,
                    workflow_task_id=workflow_task_id,
                    agent_type=self.agent_type,
                    task_description=f"评审需求: {requirement.title}",
                    execution_time=execution_time,
                    data={"error": str(e)}
                )
            )
            
            raise Exception(f"需求评审失败: {str(e)}")
    
    def _build_review_prompt(self, requirement: RequirementDocument, memory_context: str = "") -> str:
        """构建评审提示"""
        prompt = f"""
请评审以下需求文档：

**基本信息**:
- 标题: {requirement.title}
- 优先级: {requirement.priority.value}
- 创建者: {requirement.created_by}
- 标签: {', '.join(requirement.tags) if requirement.tags else '无'}

**需求内容**:
{requirement.content}

**功能需求** ({len(requirement.functional_requirements)}项):
"""
        
        for i, req in enumerate(requirement.functional_requirements, 1):
            prompt += f"{i}. {req}\n"
        
        prompt += f"\n**非功能需求** ({len(requirement.non_functional_requirements)}项):\n"
        for i, req in enumerate(requirement.non_functional_requirements, 1):
            prompt += f"{i}. {req}\n"
        
        prompt += f"\n**验收标准** ({len(requirement.acceptance_criteria)}项):\n"
        for i, criteria in enumerate(requirement.acceptance_criteria, 1):
            prompt += f"{i}. {criteria}\n"
        
        if requirement.analysis_result:
            prompt += f"\n**分析结果摘要**:\n{requirement.analysis_result.get('analysis_summary', '无')}\n"
            
            if requirement.analysis_result.get('potential_risks'):
                prompt += f"\n**潜在风险**:\n"
                for risk in requirement.analysis_result['potential_risks']:
                    prompt += f"- {risk}\n"
        
        if memory_context:
            prompt += f"\n{memory_context}\n"
        
        prompt += """
请根据评审标准和相关经验对此需求进行全面评审，并以JSON格式返回评审结果。
重点关注需求的完整性、一致性、可行性和可测试性。

输出示例：
{
  "approved": true,
  "score": 85,
  "comments": ["评审意见1", "评审意见2"],
  "suggestions": ["建议1", "建议2"],
  "issues": ["问题1", "问题2"],
  "strengths": ["优点1", "优点2"]
}
"""
        
        return prompt
    
    def _parse_review_result(self, response) -> Dict[str, Any]:
        """解析评审结果"""
        try:
            response_text = str(response).strip()
            
            # 尝试找到JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证和补充必要字段
                default_result = {
                    "approved": False,
                    "score": 0,
                    "comments": [],
                    "suggestions": [],
                    "issues": [],
                    "strengths": []
                }
                
                for key, default_value in default_result.items():
                    if key not in result:
                        result[key] = default_value
                
                # 确保score在合理范围内
                if not isinstance(result["score"], (int, float)) or result["score"] < 0:
                    result["score"] = 0
                elif result["score"] > 100:
                    result["score"] = 100
                
                return result
            else:
                return self._create_default_review_result(response_text)
                
        except json.JSONDecodeError as e:
            print(f"评审结果JSON解析失败: {e}")
            return self._create_default_review_result(str(response))
        except Exception as e:
            print(f"评审结果解析异常: {e}")
            return self._create_default_review_result(str(response))
    
    def _create_default_review_result(self, response_text: str) -> Dict[str, Any]:
        """创建默认评审结果"""
        return {
            "approved": False,
            "score": 50,
            "comments": [f"评审结果解析失败: {response_text[:100]}..."],
            "suggestions": ["需要重新评审"],
            "issues": ["评审结果格式不正确"],
            "strengths": []
        }