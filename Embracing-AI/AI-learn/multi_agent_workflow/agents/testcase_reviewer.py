"""
测试用例评审智能体 - 评审测试用例质量和覆盖度
"""
import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import time

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from storage.models import TestCase, ReviewResult, AgentType, RequirementDocument
from storage.simple_chroma_manager import simple_chroma_manager as chroma_manager
from core.events import global_event_bus, AgentTaskEvent
from core.memory_manager import global_memory_manager
from config.settings import settings


class TestCaseReviewer:
    """测试用例评审智能体"""
    
    def __init__(self, model_client):
        self.model_client = model_client
        self.agent_type = AgentType.TESTCASE_REVIEWER
        
        # 初始化记忆管理器
        self.memory_manager = global_memory_manager
        
        # 创建AutoGen智能体
        self.agent = AssistantAgent(
            name="testcase_reviewer",
            model_client=model_client,
            system_message=self._get_system_message(),
            tools=[],
            memory=[]
        )
    
    def _get_system_message(self) -> str:
        """获取系统提示消息"""
        return """
你是一个资深的测试用例评审专家，负责评估测试用例的质量和覆盖度。你的职责包括：

1. **完整性评估**：检查测试用例是否完整、清晰
2. **覆盖度分析**：评估测试覆盖度是否充分
3. **可执行性验证**：确保测试用例可以实际执行
4. **有效性检查**：验证测试用例能够有效发现缺陷
5. **质量评分**：给出客观的质量评分(0-100分)

**评审标准**：
- 测试用例完整性 (25分)：标题、描述、步骤、预期结果是否完整
- 测试步骤清晰度 (20分)：步骤是否清晰、可操作
- 预期结果准确性 (20分)：预期结果是否明确、可验证
- 测试覆盖度 (20分)：是否覆盖主要功能和边界情况
- 可执行性 (15分)：测试用例是否可以实际执行

**测试用例质量要求**：
- 标题简洁明确，能够概括测试内容
- 描述清楚测试目的和范围
- 前置条件明确且可达成
- 测试步骤详细、有序、可操作
- 预期结果具体、可验证
- 测试数据合理、充分

**评审输出格式**：
请以JSON格式输出评审结果，包含以下字段：
- approved: 是否通过评审 (boolean)
- score: 总体评分 (0-100)
- comments: 评审意见列表
- suggestions: 改进建议列表
- issues: 发现的问题列表
- strengths: 测试用例优点列表
- coverage_analysis: 覆盖度分析
- detailed_scores: 各项详细评分

**评审原则**：
- 客观公正，基于质量标准
- 提供建设性的改进建议
- 关注实际可执行性
- 考虑测试效率和成本
"""
    
    async def review_testcase(self, testcase: TestCase, requirement: Optional[RequirementDocument],
                            workflow_task_id: str) -> ReviewResult:
        """评审单个测试用例"""
        start_time = time.time()
        
        # 发布任务开始事件
        await global_event_bus.publish(
            AgentTaskEvent.task_started(
                agent_type=self.agent_type,
                task_description=f"评审测试用例: {testcase.title}",
                workflow_task_id=workflow_task_id
            )
        )
        
        try:
            # 获取相关记忆
            memory_context = await self.memory_manager.get_relevant_memories(
                self.agent_type,
                f"测试用例评审 {testcase.title} {testcase.test_type}"
            )
            
            # 构建评审提示
            review_prompt = self._build_review_prompt(testcase, requirement, memory_context)
            
            # 调用智能体进行评审
            # 使用正确的AutoGen方式调用智能体
            stream = self.agent.run_stream(task=review_prompt)
            response_content = ""
            async for message in stream:
                if hasattr(message, 'content'):
                    response_content += str(message.content)
                else:
                    response_content += str(message)

            response = response_content
            
            # 解析评审结果
            review_data = self._parse_review_result(response)
            
            # 创建评审结果对象
            review_result = ReviewResult(
                reviewer_agent=self.agent_type,
                target_id=testcase.id,
                target_type="testcase",
                approved=review_data.get("approved", False),
                score=review_data.get("score", 0),
                comments=review_data.get("comments", []),
                suggestions=review_data.get("suggestions", []),
                issues=review_data.get("issues", []),
                review_duration=time.time() - start_time
            )
            
            # 存储评审结果
            await chroma_manager.store_review_result(review_result)
            
            # 存储评审经验到记忆
            await self.memory_manager.store_review_memory(
                review_result,
                f"{testcase.title}: {testcase.description}"
            )
            
            execution_time = time.time() - start_time
            
            # 发布任务完成事件
            await global_event_bus.publish(
                AgentTaskEvent.task_completed(
                    agent_type=self.agent_type,
                    task_description=f"评审测试用例: {testcase.title}",
                    workflow_task_id=workflow_task_id,
                    execution_time=execution_time,
                    result={
                        "approved": review_result.approved,
                        "score": review_result.score,
                        "review_id": review_result.id
                    }
                )
            )
            
            return review_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 发布任务失败事件
            await global_event_bus.publish(
                AgentTaskEvent(
                    id="",
                    event_type="agent_task_failed",
                    timestamp=datetime.now(),
                    source_agent=self.agent_type,
                    workflow_task_id=workflow_task_id,
                    agent_type=self.agent_type,
                    task_description=f"评审测试用例: {testcase.title}",
                    execution_time=execution_time,
                    data={"error": str(e)}
                )
            )
            
            raise Exception(f"测试用例评审失败: {str(e)}")
    
    async def review_testcase_suite(self, testcases: List[TestCase], 
                                  requirement: Optional[RequirementDocument],
                                  workflow_task_id: str) -> Dict[str, Any]:
        """评审测试用例套件"""
        start_time = time.time()
        
        # 发布任务开始事件
        await global_event_bus.publish(
            AgentTaskEvent.task_started(
                agent_type=self.agent_type,
                task_description=f"评审测试用例套件 ({len(testcases)}个用例)",
                workflow_task_id=workflow_task_id
            )
        )
        
        try:
            # 单独评审每个测试用例
            individual_reviews = []
            for testcase in testcases:
                review = await self.review_testcase(testcase, requirement, workflow_task_id)
                individual_reviews.append(review)
            
            # 进行套件级别的整体评审
            suite_review = await self._review_suite_coverage(testcases, requirement, individual_reviews)
            
            execution_time = time.time() - start_time
            
            # 发布任务完成事件
            await global_event_bus.publish(
                AgentTaskEvent.task_completed(
                    agent_type=self.agent_type,
                    task_description=f"评审测试用例套件 ({len(testcases)}个用例)",
                    workflow_task_id=workflow_task_id,
                    execution_time=execution_time,
                    result=suite_review
                )
            )
            
            return {
                "individual_reviews": individual_reviews,
                "suite_review": suite_review,
                "summary": {
                    "total_testcases": len(testcases),
                    "approved_count": sum(1 for r in individual_reviews if r.approved),
                    "average_score": sum(r.score for r in individual_reviews) / len(individual_reviews) if individual_reviews else 0,
                    "overall_approved": suite_review.get("overall_approved", False)
                }
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # 发布任务失败事件
            await global_event_bus.publish(
                AgentTaskEvent(
                    id="",
                    event_type="agent_task_failed",
                    timestamp=datetime.now(),
                    source_agent=self.agent_type,
                    workflow_task_id=workflow_task_id,
                    agent_type=self.agent_type,
                    task_description=f"评审测试用例套件 ({len(testcases)}个用例)",
                    execution_time=execution_time,
                    data={"error": str(e)}
                )
            )
            
            raise Exception(f"测试用例套件评审失败: {str(e)}")
    
    def _build_review_prompt(self, testcase: TestCase, 
                           requirement: Optional[RequirementDocument],
                           memory_context: str = "") -> str:
        """构建评审提示"""
        prompt = f"""
请评审以下测试用例：

**测试用例基本信息**:
- 标题: {testcase.title}
- 描述: {testcase.description}
- 测试类型: {testcase.test_type}
- 测试级别: {testcase.test_level}
- 优先级: {testcase.priority.value}
- 标签: {', '.join(testcase.tags) if testcase.tags else '无'}

**前置条件** ({len(testcase.preconditions)}项):
"""
        
        for i, condition in enumerate(testcase.preconditions, 1):
            prompt += f"{i}. {condition}\n"
        
        prompt += f"\n**测试步骤** ({len(testcase.test_steps)}步):\n"
        for i, step in enumerate(testcase.test_steps, 1):
            if isinstance(step, dict):
                action = step.get("action", "")
                expected = step.get("expected", "")
                prompt += f"{i}. 操作: {action}\n"
                if expected:
                    prompt += f"   预期: {expected}\n"
            else:
                prompt += f"{i}. {step}\n"
        
        prompt += f"\n**预期结果** ({len(testcase.expected_results)}项):\n"
        for i, result in enumerate(testcase.expected_results, 1):
            prompt += f"{i}. {result}\n"
        
        if testcase.test_data:
            prompt += f"\n**测试数据**:\n{json.dumps(testcase.test_data, ensure_ascii=False, indent=2)}\n"
        
        if requirement:
            prompt += f"""
**关联需求信息**:
- 需求标题: {requirement.title}
- 功能需求数量: {len(requirement.functional_requirements)}
- 验收标准数量: {len(requirement.acceptance_criteria)}
"""
        
        if memory_context:
            prompt += f"\n{memory_context}\n"
        
        prompt += """
请根据评审标准和相关经验对此测试用例进行全面评审，并以JSON格式返回评审结果。
重点关注测试用例的完整性、清晰度、可执行性和有效性。
"""
        
        return prompt
    
    async def _review_suite_coverage(self, testcases: List[TestCase],
                                   requirement: Optional[RequirementDocument],
                                   individual_reviews: List[ReviewResult]) -> Dict[str, Any]:
        """评审测试用例套件覆盖度"""
        try:
            # 构建套件覆盖度分析提示
            coverage_prompt = self._build_coverage_prompt(testcases, requirement, individual_reviews)
            
            # 调用智能体进行覆盖度分析
            # 使用正确的AutoGen方式调用智能体
            stream = self.agent.run_stream(task=coverage_prompt)
            response_content = ""
            async for message in stream:
                if hasattr(message, 'content'):
                    response_content += str(message.content)
                else:
                    response_content += str(message)

            response = response_content
            
            # 解析覆盖度分析结果
            coverage_analysis = self._parse_coverage_result(response)
            
            return coverage_analysis
            
        except Exception as e:
            print(f"套件覆盖度分析失败: {e}")
            return self._create_default_coverage_analysis(testcases, individual_reviews)
    
    def _build_coverage_prompt(self, testcases: List[TestCase],
                             requirement: Optional[RequirementDocument],
                             individual_reviews: List[ReviewResult]) -> str:
        """构建覆盖度分析提示"""
        prompt = f"""
请分析以下测试用例套件的覆盖度：

**测试用例套件概览**:
- 总用例数: {len(testcases)}
- 通过评审数: {sum(1 for r in individual_reviews if r.approved)}
- 平均评分: {sum(r.score for r in individual_reviews) / len(individual_reviews) if individual_reviews else 0:.1f}

**测试用例分布**:
"""
        
        # 按测试类型分组统计
        type_counts = {}
        level_counts = {}
        priority_counts = {}
        
        for testcase in testcases:
            type_counts[testcase.test_type] = type_counts.get(testcase.test_type, 0) + 1
            level_counts[testcase.test_level] = level_counts.get(testcase.test_level, 0) + 1
            priority_counts[testcase.priority.value] = priority_counts.get(testcase.priority.value, 0) + 1
        
        prompt += "测试类型分布:\n"
        for test_type, count in type_counts.items():
            prompt += f"- {test_type}: {count}个\n"
        
        prompt += "\n测试级别分布:\n"
        for test_level, count in level_counts.items():
            prompt += f"- {test_level}: {count}个\n"
        
        prompt += "\n优先级分布:\n"
        for priority, count in priority_counts.items():
            prompt += f"- {priority}: {count}个\n"
        
        if requirement:
            prompt += f"""
**关联需求信息**:
- 需求标题: {requirement.title}
- 功能需求: {len(requirement.functional_requirements)}项
- 非功能需求: {len(requirement.non_functional_requirements)}项
- 验收标准: {len(requirement.acceptance_criteria)}项
"""
        
        prompt += """
**覆盖度分析要求**:
请分析测试用例套件是否充分覆盖了需求，包括：
1. 功能覆盖度 - 是否覆盖所有功能需求
2. 场景覆盖度 - 是否覆盖正常、边界、异常场景
3. 测试类型覆盖度 - 是否包含必要的测试类型
4. 优先级分布合理性 - 优先级分配是否合理

请以JSON格式返回分析结果，包含：
- overall_approved: 整体是否通过 (boolean)
- coverage_score: 覆盖度评分 (0-100)
- functional_coverage: 功能覆盖度分析
- scenario_coverage: 场景覆盖度分析
- type_coverage: 测试类型覆盖度分析
- missing_areas: 缺失的测试领域
- recommendations: 改进建议
"""
        
        return prompt
    
    def _parse_coverage_result(self, response) -> Dict[str, Any]:
        """解析覆盖度分析结果"""
        try:
            # 从响应中提取JSON内容
            response_text = str(response)
            
            # 尝试找到JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证和标准化字段
                result["overall_approved"] = result.get("overall_approved", False)
                result["coverage_score"] = max(0, min(100, result.get("coverage_score", 0)))
                result["functional_coverage"] = result.get("functional_coverage", "需要进一步分析")
                result["scenario_coverage"] = result.get("scenario_coverage", "需要进一步分析")
                result["type_coverage"] = result.get("type_coverage", "需要进一步分析")
                result["missing_areas"] = result.get("missing_areas", [])
                result["recommendations"] = result.get("recommendations", [])
                
                return result
            else:
                return self._create_default_coverage_result(str(response))
                
        except json.JSONDecodeError:
            return self._create_default_coverage_result(str(response))
    
    def _create_default_coverage_result(self, response_text: str) -> Dict[str, Any]:
        """创建默认覆盖度分析结果"""
        return {
            "overall_approved": False,
            "coverage_score": 60,
            "functional_coverage": "基础功能已覆盖，需要补充边界测试",
            "scenario_coverage": "正常场景已覆盖，异常场景需要加强",
            "type_coverage": "功能测试充分，其他类型测试需要补充",
            "missing_areas": ["性能测试", "安全测试", "边界值测试"],
            "recommendations": [
                "增加边界值和异常场景测试",
                "补充性能和安全测试用例",
                "完善测试数据准备"
            ]
        }
    
    def _create_default_coverage_analysis(self, testcases: List[TestCase],
                                        individual_reviews: List[ReviewResult]) -> Dict[str, Any]:
        """创建默认覆盖度分析"""
        approved_count = sum(1 for r in individual_reviews if r.approved)
        average_score = sum(r.score for r in individual_reviews) / len(individual_reviews) if individual_reviews else 0
        
        return {
            "overall_approved": approved_count >= len(testcases) * 0.8,  # 80%通过率
            "coverage_score": min(80, average_score),
            "functional_coverage": f"已生成{len(testcases)}个测试用例，通过率{approved_count/len(testcases)*100:.1f}%",
            "scenario_coverage": "基本场景已覆盖",
            "type_coverage": "测试类型分布合理",
            "missing_areas": [],
            "recommendations": ["建议进行人工复审"]
        }
    
    def _parse_review_result(self, response) -> Dict[str, Any]:
        """解析评审结果"""
        try:
            # 从响应中提取JSON内容
            response_text = str(response)
            
            # 尝试找到JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证和标准化字段
                result["approved"] = result.get("approved", False)
                result["score"] = max(0, min(100, result.get("score", 0)))
                result["comments"] = result.get("comments", [])
                result["suggestions"] = result.get("suggestions", [])
                result["issues"] = result.get("issues", [])
                
                return result
            else:
                return self._create_default_review_result(response_text)
                
        except json.JSONDecodeError:
            return self._create_default_review_result(str(response))
    
    def _create_default_review_result(self, response_text: str) -> Dict[str, Any]:
        """创建默认评审结果"""
        # 简单的启发式评分
        score = 60  # 默认中等分数
        approved = False
        
        # 基于响应内容的简单判断
        positive_keywords = ["完整", "清晰", "合理", "可执行", "通过"]
        negative_keywords = ["不完整", "模糊", "不清楚", "问题", "缺少"]
        
        positive_count = sum(1 for keyword in positive_keywords if keyword in response_text)
        negative_count = sum(1 for keyword in negative_keywords if keyword in response_text)
        
        if positive_count > negative_count:
            score = min(85, 60 + positive_count * 8)
            approved = score >= 70
        elif negative_count > positive_count:
            score = max(30, 60 - negative_count * 10)
            approved = False
        
        return {
            "approved": approved,
            "score": score,
            "comments": [f"自动评审结果: {response_text[:100]}..."],
            "suggestions": ["建议人工复审此测试用例"],
            "issues": ["评审结果解析异常"] if negative_count > 0 else [],
            "strengths": ["测试用例已生成"] if positive_count > 0 else []
        }
    
    async def batch_review_testcases(self, testcases: List[TestCase],
                                   requirement: Optional[RequirementDocument],
                                   workflow_task_id: str) -> List[ReviewResult]:
        """批量评审测试用例"""
        results = []
        
        # 并发评审（限制并发数）
        semaphore = asyncio.Semaphore(3)  # 最多3个并发评审
        
        async def review_single(testcase):
            async with semaphore:
                return await self.review_testcase(testcase, requirement, workflow_task_id)
        
        tasks = [review_single(tc) for tc in testcases]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"评审测试用例 {testcases[i].title} 失败: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def get_review_statistics(self, testcase_ids: List[str]) -> Dict[str, Any]:
        """获取评审统计信息"""
        stats = {
            "total_reviews": 0,
            "approved_count": 0,
            "average_score": 0,
            "common_issues": [],
            "improvement_areas": []
        }
        
        try:
            # 这里可以从ChromaDB查询评审历史
            # 实现统计逻辑
            pass
        except Exception as e:
            print(f"获取评审统计失败: {e}")
        
        return stats
    
    async def get_review_capabilities(self) -> Dict[str, Any]:
        """获取评审能力描述"""
        return {
            "agent_type": self.agent_type.value,
            "capabilities": [
                "测试用例完整性检查",
                "测试步骤清晰度验证",
                "预期结果准确性评估",
                "测试覆盖度分析",
                "可执行性验证",
                "批量评审处理",
                "套件覆盖度分析"
            ],
            "evaluation_criteria": [
                "测试用例完整性 (25%)",
                "测试步骤清晰度 (20%)",
                "预期结果准确性 (20%)",
                "测试覆盖度 (20%)",
                "可执行性 (15%)"
            ],
            "output_format": "结构化评审报告和覆盖度分析"
        }
