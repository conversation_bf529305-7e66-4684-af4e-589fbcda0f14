"""
工作流编排器 - 管理多智能体协作流程
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import time
from enum import Enum

from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_core.models import ChatCompletionClient

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from storage.models import (
    RequirementDocument, WorkflowTask, WorkflowStatus, 
    AgentType, TestCase, ReviewResult
)
from storage.simple_chroma_manager import simple_chroma_manager as chroma_manager
from core.events import (
    global_event_bus, WorkflowEvent, EventType,
    AgentTaskEvent, RequirementEvent, TestCaseEvent
)
from agents.requirement_analyzer import RequirementAnalyzer
from agents.requirement_reviewer import RequirementReviewer
from agents.testcase_generator import TestCaseGenerator
from agents.testcase_reviewer import TestCaseReviewer
from config.settings import settings


class WorkflowStage(str, Enum):
    """工作流阶段枚举"""
    REQUIREMENT_ANALYSIS = "requirement_analysis"
    REQUIREMENT_REVIEW = "requirement_review"
    TESTCASE_GENERATION = "testcase_generation"
    TESTCASE_REVIEW = "testcase_review"
    COMPLETED = "completed"


class WorkflowOrchestrator:
    """工作流编排器 - 协调多智能体协作"""
    
    def __init__(self, model_client: ChatCompletionClient):
        self.model_client = model_client
        
        # 初始化智能体
        self.requirement_analyzer = RequirementAnalyzer(model_client)
        self.requirement_reviewer = RequirementReviewer(model_client)
        self.testcase_generator = TestCaseGenerator(model_client)
        self.testcase_reviewer = TestCaseReviewer(model_client)
        
        # 工作流状态管理
        self.active_workflows: Dict[str, WorkflowTask] = {}
        self.workflow_callbacks: Dict[str, List[Callable]] = {}
        
        # 事件订阅
        self._setup_event_subscriptions()
    
    def _setup_event_subscriptions(self):
        """设置事件订阅"""
        asyncio.create_task(self._subscribe_to_events())
    
    async def _subscribe_to_events(self):
        """订阅相关事件"""
        # 订阅智能体任务完成事件
        await global_event_bus.subscribe(
            EventType.AGENT_TASK_COMPLETED,
            self._handle_agent_task_completed
        )
        
        # 订阅智能体任务失败事件
        await global_event_bus.subscribe(
            EventType.AGENT_TASK_FAILED,
            self._handle_agent_task_failed
        )
    
    async def start_workflow(self, requirement: RequirementDocument,
                           callback: Optional[Callable] = None) -> WorkflowTask:
        """启动工作流"""
        # 创建工作流任务
        workflow_task = WorkflowTask(
            requirement_id=requirement.id,
            current_stage=AgentType.REQUIREMENT_ANALYZER,
            status=WorkflowStatus.IN_PROGRESS,
            started_at=datetime.now(),
            current_data={"requirement": requirement.dict()}
        )
        
        # 注册工作流
        self.active_workflows[workflow_task.id] = workflow_task
        
        # 注册回调
        if callback:
            if workflow_task.id not in self.workflow_callbacks:
                self.workflow_callbacks[workflow_task.id] = []
            self.workflow_callbacks[workflow_task.id].append(callback)
        
        # 发布工作流开始事件
        await global_event_bus.publish(
            WorkflowEvent.workflow_started(
                requirement_id=requirement.id,
                workflow_task_id=workflow_task.id
            )
        )
        
        # 开始第一阶段：需求分析
        await self._execute_requirement_analysis(workflow_task, requirement)
        
        return workflow_task
    
    async def _execute_requirement_analysis(self, workflow_task: WorkflowTask,
                                          requirement: RequirementDocument):
        """执行需求分析阶段"""
        try:
            workflow_task.current_stage = AgentType.REQUIREMENT_ANALYZER
            workflow_task.execution_history.append({
                "stage": "requirement_analysis",
                "started_at": datetime.now().isoformat(),
                "status": "started"
            })
            
            # 调用需求分析智能体
            analysis_result = await self.requirement_analyzer.analyze_requirement(
                requirement, workflow_task.id
            )
            
            # 更新工作流数据
            workflow_task.current_data["analysis_result"] = analysis_result
            workflow_task.execution_history[-1]["completed_at"] = datetime.now().isoformat()
            workflow_task.execution_history[-1]["status"] = "completed"
            workflow_task.execution_history[-1]["result"] = analysis_result
            
            # 进入下一阶段：需求评审
            await self._execute_requirement_review(workflow_task, requirement)
            
        except Exception as e:
            await self._handle_workflow_error(workflow_task, "requirement_analysis", str(e))
    
    async def _execute_requirement_review(self, workflow_task: WorkflowTask,
                                        requirement: RequirementDocument):
        """执行需求评审阶段"""
        try:
            workflow_task.current_stage = AgentType.REQUIREMENT_REVIEWER
            workflow_task.execution_history.append({
                "stage": "requirement_review",
                "started_at": datetime.now().isoformat(),
                "status": "started"
            })
            
            # 调用需求评审智能体
            review_result = await self.requirement_reviewer.review_requirement(
                requirement, workflow_task.id
            )
            
            # 更新工作流数据
            workflow_task.current_data["requirement_review"] = review_result.dict()
            workflow_task.execution_history[-1]["completed_at"] = datetime.now().isoformat()
            workflow_task.execution_history[-1]["status"] = "completed"
            workflow_task.execution_history[-1]["result"] = review_result.dict()
            
            # 检查评审结果
            if review_result.approved:
                # 评审通过，进入测试用例生成阶段
                await self._execute_testcase_generation(workflow_task, requirement)
            else:
                # 评审不通过，需要修改需求（这里可以实现反思模式）
                await self._handle_requirement_rejection(workflow_task, review_result)
            
        except Exception as e:
            await self._handle_workflow_error(workflow_task, "requirement_review", str(e))
    
    async def _execute_testcase_generation(self, workflow_task: WorkflowTask,
                                         requirement: RequirementDocument):
        """执行测试用例生成阶段"""
        try:
            workflow_task.current_stage = AgentType.TESTCASE_GENERATOR
            workflow_task.execution_history.append({
                "stage": "testcase_generation",
                "started_at": datetime.now().isoformat(),
                "status": "started"
            })
            
            # 调用测试用例生成智能体
            testcases = await self.testcase_generator.generate_testcases(
                requirement, workflow_task.id
            )
            
            # 更新工作流数据
            workflow_task.current_data["testcases"] = [tc.dict() for tc in testcases]
            workflow_task.execution_history[-1]["completed_at"] = datetime.now().isoformat()
            workflow_task.execution_history[-1]["status"] = "completed"
            workflow_task.execution_history[-1]["result"] = {
                "testcase_count": len(testcases),
                "testcase_ids": [tc.id for tc in testcases]
            }
            
            # 进入最后阶段：测试用例评审
            await self._execute_testcase_review(workflow_task, requirement, testcases)
            
        except Exception as e:
            await self._handle_workflow_error(workflow_task, "testcase_generation", str(e))
    
    async def _execute_testcase_review(self, workflow_task: WorkflowTask,
                                     requirement: RequirementDocument,
                                     testcases: List[TestCase]):
        """执行测试用例评审阶段"""
        try:
            workflow_task.current_stage = AgentType.TESTCASE_REVIEWER
            workflow_task.execution_history.append({
                "stage": "testcase_review",
                "started_at": datetime.now().isoformat(),
                "status": "started"
            })
            
            # 调用测试用例评审智能体
            review_result = await self.testcase_reviewer.review_testcase_suite(
                testcases, requirement, workflow_task.id
            )
            
            # 更新工作流数据
            workflow_task.current_data["testcase_review"] = review_result
            workflow_task.execution_history[-1]["completed_at"] = datetime.now().isoformat()
            workflow_task.execution_history[-1]["status"] = "completed"
            workflow_task.execution_history[-1]["result"] = review_result
            
            # 完成工作流
            await self._complete_workflow(workflow_task, review_result)
            
        except Exception as e:
            await self._handle_workflow_error(workflow_task, "testcase_review", str(e))
    
    async def _complete_workflow(self, workflow_task: WorkflowTask, final_result: Dict[str, Any]):
        """完成工作流"""
        workflow_task.status = WorkflowStatus.COMPLETED
        workflow_task.completed_at = datetime.now()
        workflow_task.current_data["final_result"] = final_result
        
        # 发布工作流完成事件
        await global_event_bus.publish(
            WorkflowEvent.workflow_completed(
                requirement_id=workflow_task.requirement_id,
                workflow_task_id=workflow_task.id
            )
        )
        
        # 调用回调函数
        if workflow_task.id in self.workflow_callbacks:
            for callback in self.workflow_callbacks[workflow_task.id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(workflow_task)
                    else:
                        callback(workflow_task)
                except Exception as e:
                    print(f"工作流回调执行失败: {e}")
        
        # 清理工作流
        if workflow_task.id in self.active_workflows:
            del self.active_workflows[workflow_task.id]
        if workflow_task.id in self.workflow_callbacks:
            del self.workflow_callbacks[workflow_task.id]
    
    async def _handle_requirement_rejection(self, workflow_task: WorkflowTask,
                                          review_result: ReviewResult):
        """处理需求评审不通过的情况"""
        # 实现反思模式：基于评审意见改进需求
        workflow_task.execution_history.append({
            "stage": "requirement_improvement",
            "started_at": datetime.now().isoformat(),
            "status": "started",
            "reason": "需求评审不通过",
            "review_feedback": review_result.dict()
        })
        
        # 这里可以实现自动改进逻辑或人工介入
        # 暂时标记为需要人工处理
        workflow_task.status = WorkflowStatus.FAILED
        workflow_task.error_message = f"需求评审不通过，评分: {review_result.score}"
        
        await self._complete_workflow(workflow_task, {"status": "rejected", "review": review_result.dict()})
    
    async def _handle_workflow_error(self, workflow_task: WorkflowTask, stage: str, error: str):
        """处理工作流错误"""
        workflow_task.status = WorkflowStatus.FAILED
        workflow_task.error_message = f"{stage}阶段失败: {error}"
        workflow_task.retry_count += 1
        
        # 更新执行历史
        if workflow_task.execution_history:
            workflow_task.execution_history[-1]["status"] = "failed"
            workflow_task.execution_history[-1]["error"] = error
            workflow_task.execution_history[-1]["failed_at"] = datetime.now().isoformat()
        
        # 发布工作流失败事件
        await global_event_bus.publish(
            WorkflowEvent(
                id="",
                event_type=EventType.WORKFLOW_FAILED,
                timestamp=datetime.now(),
                source_agent=None,
                workflow_task_id=workflow_task.id,
                requirement_id=workflow_task.requirement_id,
                current_stage=workflow_task.current_stage,
                status=WorkflowStatus.FAILED,
                data={"error": error, "stage": stage}
            )
        )
        
        # 决定是否重试
        if workflow_task.retry_count < 3:
            print(f"工作流 {workflow_task.id} 将在5秒后重试...")
            await asyncio.sleep(5)

            # 重置状态并重试
            workflow_task.status = WorkflowStatus.IN_PROGRESS

            # 根据失败的阶段重新执行
            requirement = RequirementDocument.parse_obj(workflow_task.current_data["requirement"])

            if stage == "requirement_analysis":
                await self._execute_requirement_analysis(workflow_task, requirement)
            elif stage == "requirement_review":
                await self._execute_requirement_review(workflow_task, requirement)
            elif stage == "testcase_generation":
                await self._execute_testcase_generation(workflow_task, requirement)
            elif stage == "testcase_review":
                testcases = [TestCase.parse_obj(tc) for tc in workflow_task.current_data.get("testcases", [])]
                await self._execute_testcase_review(workflow_task, requirement, testcases)
        else:
            await self._complete_workflow(workflow_task, {"status": "failed", "error": error})
    
    async def _handle_agent_task_completed(self, event: AgentTaskEvent):
        """处理智能体任务完成事件"""
        print(f"智能体任务完成: {event.agent_type.value} - {event.task_description}")
    
    async def _handle_agent_task_failed(self, event: AgentTaskEvent):
        """处理智能体任务失败事件"""
        print(f"智能体任务失败: {event.agent_type.value} - {event.data.get('error', '未知错误')}")
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[WorkflowTask]:
        """获取工作流状态"""
        return self.active_workflows.get(workflow_id)
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """取消工作流"""
        if workflow_id in self.active_workflows:
            workflow_task = self.active_workflows[workflow_id]
            workflow_task.status = WorkflowStatus.CANCELLED
            
            # 发布取消事件
            await global_event_bus.publish(
                WorkflowEvent(
                    id="",
                    event_type=EventType.WORKFLOW_CANCELLED,
                    timestamp=datetime.now(),
                    source_agent=None,
                    workflow_task_id=workflow_id,
                    requirement_id=workflow_task.requirement_id,
                    current_stage=workflow_task.current_stage,
                    status=WorkflowStatus.CANCELLED,
                    data={"message": "工作流已取消"}
                )
            )
            
            # 清理资源
            del self.active_workflows[workflow_id]
            if workflow_id in self.workflow_callbacks:
                del self.workflow_callbacks[workflow_id]
            
            return True
        return False
    
    async def get_active_workflows(self) -> List[WorkflowTask]:
        """获取所有活跃工作流"""
        return list(self.active_workflows.values())
    
    async def get_workflow_statistics(self) -> Dict[str, Any]:
        """获取工作流统计信息"""
        active_count = len(self.active_workflows)
        stage_counts = {}
        
        for workflow in self.active_workflows.values():
            stage = workflow.current_stage.value
            stage_counts[stage] = stage_counts.get(stage, 0) + 1
        
        return {
            "active_workflows": active_count,
            "stage_distribution": stage_counts,
            "total_processed": 0,  # 这里可以从数据库获取历史统计
            "success_rate": 0.0    # 这里可以计算成功率
        }


# 全局工作流编排器实例
workflow_orchestrator = None

def get_orchestrator(model_client: ChatCompletionClient) -> WorkflowOrchestrator:
    """获取工作流编排器实例"""
    global workflow_orchestrator
    if workflow_orchestrator is None:
        workflow_orchestrator = WorkflowOrchestrator(model_client)
    return workflow_orchestrator