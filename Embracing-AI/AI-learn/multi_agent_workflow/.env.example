# 模型提供商选择 (ollama/openai/azure)
MODEL_PROVIDER=ollama

# Ollama本地模型配置 (推荐)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=deepseek-r1:1.5b
OLLAMA_TIMEOUT=60
OLLAMA_MAX_TOKENS=4096
OLLAMA_CONTEXT_LENGTH=8192

# OpenAI API配置 (备用)
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_MODEL=gpt-4o-mini

# Azure OpenAI配置 (备用)
# AZURE_OPENAI_API_KEY=your_azure_openai_api_key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_API_VERSION=2024-02-15-preview

# ChromaDB配置
CHROMA_DB_PATH=./data/chroma_db
CHROMA_COLLECTION_NAME=workflow_memory

# 系统配置
LOG_LEVEL=INFO
MAX_CONCURRENT_AGENTS=4
WORKFLOW_TIMEOUT=300

# 智能体配置 (针对本地模型优化)
REQUIREMENT_ANALYZER_TEMPERATURE=0.1
REQUIREMENT_REVIEWER_TEMPERATURE=0.1
TESTCASE_GENERATOR_TEMPERATURE=0.2
TESTCASE_REVIEWER_TEMPERATURE=0.1