"""
事件系统模块 - 基于AutoGen Core的事件驱动架构
"""
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import asyncio
import uuid
from autogen_core import MessageContext
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from storage.models import AgentType, WorkflowStatus


class EventType(str, Enum):
    """事件类型枚举"""
    # 工作流事件
    WORKFLOW_STARTED = "workflow_started"
    WORKFLOW_COMPLETED = "workflow_completed"
    WORKFLOW_FAILED = "workflow_failed"
    WORKFLOW_CANCELLED = "workflow_cancelled"
    
    # 智能体事件
    AGENT_TASK_STARTED = "agent_task_started"
    AGENT_TASK_COMPLETED = "agent_task_completed"
    AGENT_TASK_FAILED = "agent_task_failed"
    
    # 需求相关事件
    REQUIREMENT_SUBMITTED = "requirement_submitted"
    REQUIREMENT_ANALYZED = "requirement_analyzed"
    REQUIREMENT_REVIEWED = "requirement_reviewed"
    
    # 测试用例相关事件
    TESTCASE_GENERATED = "testcase_generated"
    TESTCASE_REVIEWED = "testcase_reviewed"
    
    # 系统事件
    SYSTEM_ERROR = "system_error"
    SYSTEM_WARNING = "system_warning"


@dataclass
class BaseEvent:
    """基础事件类"""
    id: str
    event_type: EventType
    timestamp: datetime
    source_agent: Optional[AgentType]
    workflow_task_id: Optional[str]
    data: Dict[str, Any]
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.timestamp:
            self.timestamp = datetime.now()


@dataclass
class WorkflowEvent(BaseEvent):
    """工作流事件"""
    requirement_id: str
    current_stage: AgentType
    status: WorkflowStatus
    
    @classmethod
    def workflow_started(cls, requirement_id: str, workflow_task_id: str) -> 'WorkflowEvent':
        return cls(
            id=str(uuid.uuid4()),
            event_type=EventType.WORKFLOW_STARTED,
            timestamp=datetime.now(),
            source_agent=None,
            workflow_task_id=workflow_task_id,
            requirement_id=requirement_id,
            current_stage=AgentType.REQUIREMENT_ANALYZER,
            status=WorkflowStatus.IN_PROGRESS,
            data={"message": "工作流已启动"}
        )
    
    @classmethod
    def workflow_completed(cls, requirement_id: str, workflow_task_id: str) -> 'WorkflowEvent':
        return cls(
            id=str(uuid.uuid4()),
            event_type=EventType.WORKFLOW_COMPLETED,
            timestamp=datetime.now(),
            source_agent=None,
            workflow_task_id=workflow_task_id,
            requirement_id=requirement_id,
            current_stage=AgentType.TESTCASE_REVIEWER,
            status=WorkflowStatus.COMPLETED,
            data={"message": "工作流已完成"}
        )


@dataclass
class AgentTaskEvent(BaseEvent):
    """智能体任务事件"""
    agent_type: AgentType
    task_description: str
    execution_time: Optional[float] = None
    
    @classmethod
    def task_started(cls, agent_type: AgentType, task_description: str, 
                    workflow_task_id: str) -> 'AgentTaskEvent':
        return cls(
            id=str(uuid.uuid4()),
            event_type=EventType.AGENT_TASK_STARTED,
            timestamp=datetime.now(),
            source_agent=agent_type,
            workflow_task_id=workflow_task_id,
            agent_type=agent_type,
            task_description=task_description,
            data={"status": "started"}
        )
    
    @classmethod
    def task_completed(cls, agent_type: AgentType, task_description: str,
                      workflow_task_id: str, execution_time: float,
                      result: Dict[str, Any]) -> 'AgentTaskEvent':
        return cls(
            id=str(uuid.uuid4()),
            event_type=EventType.AGENT_TASK_COMPLETED,
            timestamp=datetime.now(),
            source_agent=agent_type,
            workflow_task_id=workflow_task_id,
            agent_type=agent_type,
            task_description=task_description,
            execution_time=execution_time,
            data={"status": "completed", "result": result}
        )


@dataclass
class RequirementEvent(BaseEvent):
    """需求相关事件"""
    requirement_id: str
    
    @classmethod
    def requirement_analyzed(cls, requirement_id: str, workflow_task_id: str,
                           analysis_result: Dict[str, Any]) -> 'RequirementEvent':
        return cls(
            id=str(uuid.uuid4()),
            event_type=EventType.REQUIREMENT_ANALYZED,
            timestamp=datetime.now(),
            source_agent=AgentType.REQUIREMENT_ANALYZER,
            workflow_task_id=workflow_task_id,
            requirement_id=requirement_id,
            data={"analysis_result": analysis_result}
        )


@dataclass
class TestCaseEvent(BaseEvent):
    """测试用例相关事件"""
    testcase_id: str
    requirement_id: str
    
    @classmethod
    def testcase_generated(cls, testcase_id: str, requirement_id: str,
                          workflow_task_id: str, testcase_count: int) -> 'TestCaseEvent':
        return cls(
            id=str(uuid.uuid4()),
            event_type=EventType.TESTCASE_GENERATED,
            timestamp=datetime.now(),
            source_agent=AgentType.TESTCASE_GENERATOR,
            workflow_task_id=workflow_task_id,
            testcase_id=testcase_id,
            requirement_id=requirement_id,
            data={"testcase_count": testcase_count}
        )


EventT = TypeVar('EventT', bound=BaseEvent)


class EventBus:
    """事件总线 - 基于asyncio的事件发布订阅系统"""
    
    def __init__(self):
        self._subscribers: Dict[EventType, List[callable]] = {}
        self._event_history: List[BaseEvent] = []
        self._max_history_size = 1000
        
    async def subscribe(self, event_type: EventType, handler: callable):
        """订阅事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(handler)
    
    async def unsubscribe(self, event_type: EventType, handler: callable):
        """取消订阅"""
        if event_type in self._subscribers:
            try:
                self._subscribers[event_type].remove(handler)
            except ValueError:
                pass
    
    async def publish(self, event: BaseEvent):
        """发布事件"""
        # 记录事件历史
        self._event_history.append(event)
        if len(self._event_history) > self._max_history_size:
            self._event_history.pop(0)
        
        # 通知订阅者
        if event.event_type in self._subscribers:
            tasks = []
            for handler in self._subscribers[event.event_type]:
                tasks.append(self._safe_call_handler(handler, event))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _safe_call_handler(self, handler: callable, event: BaseEvent):
        """安全调用事件处理器"""
        try:
            if asyncio.iscoroutinefunction(handler):
                await handler(event)
            else:
                handler(event)
        except Exception as e:
            # 记录错误但不影响其他处理器
            print(f"事件处理器执行失败: {e}")
    
    def get_event_history(self, event_type: Optional[EventType] = None,
                         limit: int = 100) -> List[BaseEvent]:
        """获取事件历史"""
        events = self._event_history
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        return events[-limit:]
    
    def get_workflow_events(self, workflow_task_id: str) -> List[BaseEvent]:
        """获取特定工作流的事件"""
        return [e for e in self._event_history 
                if e.workflow_task_id == workflow_task_id]


class EventLogger:
    """事件日志记录器"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志记录"""
        # 订阅所有事件类型进行日志记录
        for event_type in EventType:
            asyncio.create_task(
                self.event_bus.subscribe(event_type, self._log_event)
            )
    
    async def _log_event(self, event: BaseEvent):
        """记录事件日志"""
        log_message = (
            f"[{event.timestamp.isoformat()}] "
            f"{event.event_type.value} - "
            f"Source: {event.source_agent} - "
            f"Workflow: {event.workflow_task_id}"
        )
        print(log_message)  # 实际项目中应使用proper logging


# 全局事件总线实例
global_event_bus = EventBus()