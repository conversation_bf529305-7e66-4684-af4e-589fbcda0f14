"""
模型客户端工厂 - 支持多种模型提供商
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List, AsyncIterator
from datetime import datetime

from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import (
    ChatCompletionClient,
    UserMessage,
    AssistantMessage,
    SystemMessage,
    CreateResult,
    ModelInfo,
    ModelCapabilities,
    RequestUsage,
    LLMMessage
)

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.settings import settings


class OllamaChatCompletionClient(ChatCompletionClient):
    """Ollama本地模型客户端"""

    def __init__(self,
                 model: str,
                 base_url: str = "http://localhost:11434",
                 timeout: int = 60,
                 max_tokens: int = 4096,
                 **kwargs):
        self.model = model
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_tokens = max_tokens
        self.session = None
        self._total_usage = RequestUsage(prompt_tokens=0, completion_tokens=0)
        self._actual_usage = RequestUsage(prompt_tokens=0, completion_tokens=0)

    async def _ensure_session(self):
        """确保HTTP会话存在"""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)

    async def create(self, messages: List[LLMMessage], **kwargs) -> CreateResult:
        """创建聊天完成"""
        await self._ensure_session()

        # 转换消息格式
        ollama_messages = []
        for msg in messages:
            if hasattr(msg, 'content') and hasattr(msg, 'source'):
                if msg.source == "user":
                    ollama_messages.append({"role": "user", "content": msg.content})
                elif msg.source == "assistant":
                    ollama_messages.append({"role": "assistant", "content": msg.content})
                elif msg.source == "system":
                    ollama_messages.append({"role": "system", "content": msg.content})
            elif isinstance(msg, dict):
                ollama_messages.append(msg)
            else:
                # 尝试直接转换
                ollama_messages.append({"role": "user", "content": str(msg)})

        # 验证消息格式
        if not ollama_messages:
            raise ValueError("消息列表为空")

        for i, msg in enumerate(ollama_messages):
            if not isinstance(msg, dict) or "content" not in msg:
                raise ValueError(f"消息格式错误 (索引 {i}): {msg}")
            if not msg.get("content", "").strip():
                raise ValueError(f"消息内容为空 (索引 {i}): {msg}")

        # 构建请求数据
        request_data = {
            "model": self.model,
            "messages": ollama_messages,
            "stream": False,
            "options": {
                "temperature": kwargs.get("temperature", 0.1),
                "num_predict": kwargs.get("max_tokens", self.max_tokens),
                "top_p": kwargs.get("top_p", 0.9),
                "top_k": kwargs.get("top_k", 40),
            }
        }

        try:
            # 发送请求到Ollama，设置120秒超时
            timeout = aiohttp.ClientTimeout(total=120)
            async with self.session.post(
                f"{self.base_url}/api/chat",
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=timeout
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Ollama API错误 {response.status}: {error_text}")

                result = await response.json()
                content = result.get("message", {}).get("content", "")

                # 更新使用统计
                prompt_tokens = len(str(messages)) // 4  # 粗略估算
                completion_tokens = len(content) // 4  # 粗略估算
                usage = RequestUsage(prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)
                self._actual_usage = usage
                self._total_usage = RequestUsage(
                    prompt_tokens=self._total_usage.prompt_tokens + prompt_tokens,
                    completion_tokens=self._total_usage.completion_tokens + completion_tokens
                )

                # 构建返回对象
                return CreateResult(
                    content=content,
                    usage=usage,
                    finish_reason="stop",
                    cached=False
                )

        except Exception as e:
            error_msg = str(e) if str(e) else "未知错误"
            print(f"Ollama API调用失败: {error_msg}")
            print(f"异常类型: {type(e).__name__}")
            print(f"请求数据: {request_data}")
            raise Exception(f"Ollama API调用失败: {error_msg}")
    
    async def create_stream(self, messages: List[LLMMessage], **kwargs) -> AsyncIterator[str | CreateResult]:
        """创建流式聊天完成"""
        # 简单实现：直接调用create并返回结果
        result = await self.create(messages, **kwargs)
        yield result

    async def count_tokens(self, messages: List[LLMMessage], **kwargs) -> int:
        """计算token数量"""
        # 简单估算：每4个字符约等于1个token
        total_chars = sum(len(str(msg)) for msg in messages)
        return total_chars // 4

    @property
    def model_info(self) -> dict:
        """获取模型信息"""
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "supports_streaming": True,
            "function_calling": True,  # 设置为True以通过AssistantAgent检查
            "vision": False  # AutoGen期望的键名是'vision'而不是'supports_vision'
        }

    def actual_usage(self) -> RequestUsage:
        """获取实际使用量"""
        return self._actual_usage

    def total_usage(self) -> RequestUsage:
        """获取总使用量"""
        return self._total_usage

    def remaining_tokens(self) -> int | None:
        """获取剩余token数量"""
        return None  # Ollama通常没有token限制

    def capabilities(self) -> dict:
        """获取模型能力"""
        return self.model_info

    async def close(self) -> None:
        """关闭客户端"""
        await self.cleanup()

    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
            self.session = None





class ModelClientFactory:
    """模型客户端工厂"""
    
    @staticmethod
    def create_client() -> ChatCompletionClient:
        """根据配置创建模型客户端"""
        
        if settings.is_using_ollama:
            print(f"🤖 使用Ollama本地模型: {settings.ollama_model}")
            return OllamaChatCompletionClient(
                model=settings.ollama_model,
                base_url=settings.ollama_base_url,
                timeout=settings.ollama_timeout,
                max_tokens=settings.ollama_max_tokens
            )
        
        elif settings.is_using_azure and settings.azure_openai_api_key:
            print(f"🤖 使用Azure OpenAI: {settings.openai_model}")
            return OpenAIChatCompletionClient(
                model=settings.openai_model,
                api_key=settings.azure_openai_api_key,
                base_url=settings.azure_openai_endpoint,
                api_version=settings.azure_openai_api_version,
                timeout=30.0,
                max_retries=3
            )
        
        elif settings.is_using_openai and settings.openai_api_key:
            print(f"🤖 使用OpenAI: {settings.openai_model}")
            return OpenAIChatCompletionClient(
                model=settings.openai_model,
                api_key=settings.openai_api_key,
                timeout=30.0,
                max_retries=3
            )
        
        else:
            # 默认尝试Ollama
            print(f"⚠️ 未找到有效的模型配置，尝试使用默认Ollama配置")
            return OllamaChatCompletionClient(
                model="deepseek-r1:1.5b",
                base_url="http://localhost:11434",
                timeout=60,
                max_tokens=4096
            )
    
    @staticmethod
    async def test_connection() -> bool:
        """测试模型连接"""
        try:
            client = ModelClientFactory.create_client()
            
            if isinstance(client, OllamaChatCompletionClient):
                # 测试Ollama连接
                await client._ensure_session()
                async with client.session.get(f"{client.base_url}/api/tags") as response:
                    if response.status == 200:
                        models = await response.json()
                        model_names = [m["name"] for m in models.get("models", [])]
                        if client.model in model_names:
                            print(f"✅ Ollama模型 {client.model} 连接成功")
                            return True
                        else:
                            print(f"❌ Ollama中未找到模型 {client.model}")
                            print(f"可用模型: {model_names}")
                            return False
                    else:
                        print(f"❌ Ollama连接失败: {response.status}")
                        return False
            else:
                # 测试OpenAI连接 (简单测试)
                print("✅ OpenAI客户端创建成功")
                return True
                
        except Exception as e:
            print(f"❌ 模型连接测试失败: {e}")
            return False


# 全局模型客户端实例
_global_client = None

def get_model_client() -> ChatCompletionClient:
    """获取全局模型客户端实例"""
    global _global_client
    if _global_client is None:
        _global_client = ModelClientFactory.create_client()
    return _global_client

async def cleanup_model_client():
    """清理全局模型客户端"""
    global _global_client
    if _global_client and hasattr(_global_client, 'cleanup'):
        await _global_client.cleanup()
    _global_client = None