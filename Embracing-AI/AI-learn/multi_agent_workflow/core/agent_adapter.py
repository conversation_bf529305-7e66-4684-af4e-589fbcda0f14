"""
智能体适配器 - 兼容不同模型客户端
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.settings import settings
from core.model_client import OllamaChatCompletionClient


class AgentAdapter:
    """智能体适配器 - 统一不同模型客户端的接口"""
    
    def __init__(self, name: str, model_client, system_message: str):
        self.name = name
        self.model_client = model_client
        self.system_message = system_message
        self.conversation_history = []
    
    async def run(self, task: str, **kwargs) -> str:
        """运行智能体任务"""
        try:
            # 构建消息列表
            messages = []
            
            # 添加系统消息
            if self.system_message:
                messages.append({
                    "role": "system",
                    "content": self.system_message
                })
            
            # 添加历史对话（如果需要）
            if hasattr(self, 'conversation_history') and self.conversation_history:
                messages.extend(self.conversation_history[-5:])  # 只保留最近5轮对话
            
            # 添加当前任务
            messages.append({
                "role": "user", 
                "content": task
            })
            
            # 直接调用Ollama模型
            response = await self._call_ollama(messages, **kwargs)
            
            # 保存对话历史
            self.conversation_history.append({"role": "user", "content": task})
            self.conversation_history.append({"role": "assistant", "content": str(response)})
            
            # 限制历史长度
            if len(self.conversation_history) > 10:
                self.conversation_history = self.conversation_history[-10:]
            
            return str(response)
            
        except Exception as e:
            print(f"智能体 {self.name} 执行失败: {e}")
            return f"执行失败: {str(e)}"
    
    async def _call_ollama(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """调用Ollama模型"""
        try:
            # 设置温度参数
            temperature = kwargs.get('temperature', 0.1)
            max_tokens = kwargs.get('max_tokens', settings.ollama_max_tokens)

            # 从kwargs中移除已经处理的参数，避免重复传递
            filtered_kwargs = {k: v for k, v in kwargs.items()
                             if k not in ['temperature', 'max_tokens']}

            response = await self.model_client.create(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **filtered_kwargs
            )

            return response.content if hasattr(response, 'content') else str(response)

        except Exception as e:
            print(f"Ollama调用失败: {e}")
            raise
    
    async def _call_autogen(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """调用标准AutoGen模型"""
        try:
            # 转换消息格式为AutoGen格式
            from autogen_agentchat.messages import TextMessage
            
            autogen_messages = []
            for msg in messages:
                if msg["role"] == "user":
                    autogen_messages.append(TextMessage(content=msg["content"], source="user"))
                elif msg["role"] == "assistant":
                    autogen_messages.append(TextMessage(content=msg["content"], source="assistant"))
                elif msg["role"] == "system":
                    # 系统消息通常在创建智能体时设置
                    continue
            
            # 调用模型
            response = await self.model_client.create(autogen_messages, **kwargs)
            
            return str(response)
            
        except Exception as e:
            print(f"AutoGen调用失败: {e}")
            # 如果AutoGen调用失败，尝试直接调用
            return await self._call_direct(messages, **kwargs)
    
    async def _call_direct(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """直接调用模型客户端"""
        try:
            response = await self.model_client.create(messages, **kwargs)
            return str(response)
        except Exception as e:
            print(f"直接调用失败: {e}")
            return f"模型调用失败: {str(e)}"
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []


def create_agent_adapter(name: str, model_client, system_message: str) -> AgentAdapter:
    """创建智能体适配器"""
    return AgentAdapter(name, model_client, system_message)


class EnhancedAgentAdapter(AgentAdapter):
    """增强版智能体适配器 - 支持更多功能"""
    
    def __init__(self, name: str, model_client, system_message: str, agent_type=None):
        super().__init__(name, model_client, system_message)
        self.agent_type = agent_type
        self.performance_stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "average_response_time": 0.0
        }
    
    async def run(self, task: str, **kwargs) -> str:
        """增强版运行方法 - 包含性能统计"""
        import time
        start_time = time.time()
        
        self.performance_stats["total_calls"] += 1
        
        try:
            result = await super().run(task, **kwargs)
            self.performance_stats["successful_calls"] += 1
            
            # 更新平均响应时间
            response_time = time.time() - start_time
            total_time = (self.performance_stats["average_response_time"] * 
                         (self.performance_stats["successful_calls"] - 1) + response_time)
            self.performance_stats["average_response_time"] = total_time / self.performance_stats["successful_calls"]
            
            return result
            
        except Exception as e:
            self.performance_stats["failed_calls"] += 1
            raise e
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            **self.performance_stats,
            "success_rate": (self.performance_stats["successful_calls"] / 
                           max(self.performance_stats["total_calls"], 1)) * 100
        }
    
    async def run_with_retry(self, task: str, max_retries: int = 3, **kwargs) -> str:
        """带重试的运行方法"""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                return await self.run(task, **kwargs)
            except Exception as e:
                last_error = e
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    print(f"智能体 {self.name} 第{attempt + 1}次尝试失败，{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"智能体 {self.name} 所有重试都失败了")
        
        raise last_error