"""
记忆管理模块 - 集成AutoGen的memory功能保持智能体长久记忆
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path

from autogen_core.memory import Memory, MemoryContent, MemoryMimeType, ListMemory
from autogen_ext.memory.chromadb import (
    ChromaDBVectorMemory, 
    PersistentChromaDBVectorMemoryConfig,
    SentenceTransformerEmbeddingFunctionConfig
)

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from storage.models import AgentType, RequirementDocument, TestCase, ReviewResult
from config.settings import settings


class AgentMemoryManager:
    """智能体记忆管理器"""
    
    def __init__(self):
        self.agent_memories: Dict[AgentType, Memory] = {}
        self.shared_memory: Memory = None
        self.initialized = False
    
    async def initialize(self):
        """初始化记忆系统"""
        if self.initialized:
            return
        
        # 确保数据目录存在
        settings.ensure_data_dirs()
        
        # 为每个智能体创建专用记忆
        await self._create_agent_memories()
        
        # 创建共享记忆
        await self._create_shared_memory()
        
        self.initialized = True
    
    async def _create_agent_memories(self):
        """为每个智能体创建专用记忆"""
        for agent_type in AgentType:
            # 创建向量记忆配置
            memory_config = PersistentChromaDBVectorMemoryConfig(
                collection_name=f"{agent_type.value}_memory",
                persistence_path=str(settings.chroma_db_full_path / "agent_memories"),
                k=5,  # 返回最相关的5条记录
                score_threshold=0.3,  # 相似度阈值
                embedding_function_config=SentenceTransformerEmbeddingFunctionConfig(
                    model_name="all-MiniLM-L6-v2"
                )
            )
            
            # 创建向量记忆实例
            vector_memory = ChromaDBVectorMemory(config=memory_config)
            
            # 存储到字典中
            self.agent_memories[agent_type] = vector_memory
    
    async def _create_shared_memory(self):
        """创建共享记忆"""
        shared_config = PersistentChromaDBVectorMemoryConfig(
            collection_name="shared_workflow_memory",
            persistence_path=str(settings.chroma_db_full_path / "shared_memory"),
            k=10,  # 共享记忆返回更多相关记录
            score_threshold=0.2,  # 更宽松的阈值
            embedding_function_config=SentenceTransformerEmbeddingFunctionConfig(
                model_name="all-MiniLM-L6-v2"
            )
        )
        
        self.shared_memory = ChromaDBVectorMemory(config=shared_config)
    
    async def add_agent_memory(self, agent_type: AgentType, content: str, 
                             metadata: Optional[Dict[str, Any]] = None):
        """为特定智能体添加记忆"""
        if not self.initialized:
            await self.initialize()
        
        if agent_type not in self.agent_memories:
            return
        
        # 准备记忆内容
        memory_content = MemoryContent(
            content=content,
            mime_type=MemoryMimeType.TEXT
        )
        
        # 添加元数据
        if metadata:
            memory_content.metadata = metadata
        
        # 添加时间戳
        memory_content.metadata = memory_content.metadata or {}
        memory_content.metadata["timestamp"] = datetime.now().isoformat()
        memory_content.metadata["agent_type"] = agent_type.value
        
        # 存储记忆
        await self.agent_memories[agent_type].add(memory_content)
    
    async def add_shared_memory(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """添加共享记忆"""
        if not self.initialized:
            await self.initialize()
        
        # 准备记忆内容
        memory_content = MemoryContent(
            content=content,
            mime_type=MemoryMimeType.TEXT
        )
        
        # 添加元数据
        if metadata:
            memory_content.metadata = metadata
        
        # 添加时间戳
        memory_content.metadata = memory_content.metadata or {}
        memory_content.metadata["timestamp"] = datetime.now().isoformat()
        memory_content.metadata["type"] = "shared"
        
        # 存储记忆
        await self.shared_memory.add(memory_content)
    
    async def query_agent_memory(self, agent_type: AgentType, query: str, 
                               limit: int = 5) -> List[Dict[str, Any]]:
        """查询特定智能体的记忆"""
        if not self.initialized:
            await self.initialize()
        
        if agent_type not in self.agent_memories:
            return []
        
        try:
            # 查询记忆
            results = await self.agent_memories[agent_type].query(query, limit)
            
            # 格式化结果
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "content": result.content,
                    "metadata": result.metadata,
                    "relevance_score": getattr(result, 'score', 0.0)
                })
            
            return formatted_results
            
        except Exception as e:
            print(f"查询智能体记忆失败: {e}")
            return []
    
    async def query_shared_memory(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """查询共享记忆"""
        if not self.initialized:
            await self.initialize()
        
        try:
            # 查询共享记忆
            results = await self.shared_memory.query(query, limit)
            
            # 格式化结果
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "content": result.content,
                    "metadata": result.metadata,
                    "relevance_score": getattr(result, 'score', 0.0)
                })
            
            return formatted_results
            
        except Exception as e:
            print(f"查询共享记忆失败: {e}")
            return []
    
    async def store_requirement_memory(self, requirement: RequirementDocument, 
                                    analysis_result: Dict[str, Any]):
        """存储需求相关记忆"""
        # 为需求分析智能体存储记忆
        analysis_memory = f"""
需求分析经验:
标题: {requirement.title}
内容: {requirement.content}
分析结果: {analysis_result.get('analysis_summary', '')}
功能需求: {', '.join(requirement.functional_requirements)}
验收标准: {', '.join(requirement.acceptance_criteria)}
优先级: {requirement.priority.value}
"""
        
        await self.add_agent_memory(
            AgentType.REQUIREMENT_ANALYZER,
            analysis_memory,
            {
                "requirement_id": requirement.id,
                "type": "requirement_analysis",
                "priority": requirement.priority.value,
                "tags": ",".join(requirement.tags) if requirement.tags else ""
            }
        )
        
        # 存储到共享记忆
        shared_content = f"需求分析案例: {requirement.title} - {analysis_result.get('analysis_summary', '')}"
        await self.add_shared_memory(
            shared_content,
            {
                "requirement_id": requirement.id,
                "type": "requirement_case",
                "agent": "requirement_analyzer"
            }
        )
    
    async def store_review_memory(self, review_result: ReviewResult, target_content: str):
        """存储评审相关记忆"""
        # 确定评审智能体类型
        reviewer_agent = review_result.reviewer_agent
        
        # 构建评审记忆内容
        review_memory = f"""
评审经验:
目标类型: {review_result.target_type}
评审结果: {'通过' if review_result.approved else '不通过'}
评分: {review_result.score}
评审意见: {'; '.join(review_result.comments)}
改进建议: {'; '.join(review_result.suggestions)}
发现问题: {'; '.join(review_result.issues)}
目标内容摘要: {target_content[:200]}...
"""
        
        await self.add_agent_memory(
            reviewer_agent,
            review_memory,
            {
                "review_id": review_result.id,
                "type": "review_experience",
                "target_type": review_result.target_type,
                "approved": review_result.approved,
                "score": review_result.score
            }
        )
        
        # 存储到共享记忆
        shared_content = f"评审案例: {review_result.target_type} - 评分{review_result.score} - {'通过' if review_result.approved else '不通过'}"
        await self.add_shared_memory(
            shared_content,
            {
                "review_id": review_result.id,
                "type": "review_case",
                "agent": reviewer_agent.value
            }
        )
    
    async def store_testcase_memory(self, testcase: TestCase, requirement: RequirementDocument):
        """存储测试用例相关记忆"""
        # 为测试用例生成智能体存储记忆
        testcase_memory = f"""
测试用例设计经验:
需求: {requirement.title}
测试用例: {testcase.title}
测试类型: {testcase.test_type}
测试级别: {testcase.test_level}
优先级: {testcase.priority.value}
前置条件: {'; '.join(testcase.preconditions)}
测试步骤数: {len(testcase.test_steps)}
预期结果: {'; '.join(testcase.expected_results)}
标签: {', '.join(testcase.tags)}
"""
        
        await self.add_agent_memory(
            AgentType.TESTCASE_GENERATOR,
            testcase_memory,
            {
                "testcase_id": testcase.id,
                "requirement_id": testcase.requirement_id,
                "type": "testcase_design",
                "test_type": testcase.test_type,
                "test_level": testcase.test_level,
                "priority": testcase.priority.value
            }
        )
        
        # 存储到共享记忆
        shared_content = f"测试用例设计: {testcase.title} - {testcase.test_type} - {testcase.test_level}"
        await self.add_shared_memory(
            shared_content,
            {
                "testcase_id": testcase.id,
                "type": "testcase_case",
                "agent": "testcase_generator"
            }
        )
    
    async def get_relevant_memories(self, agent_type: AgentType, context: str) -> str:
        """获取相关记忆并格式化为上下文"""
        # 查询智能体专用记忆
        agent_memories = await self.query_agent_memory(agent_type, context, limit=3)
        
        # 查询共享记忆
        shared_memories = await self.query_shared_memory(context, limit=2)
        
        # 格式化记忆内容
        memory_context = ""
        
        if agent_memories:
            memory_context += "**相关经验记忆**:\n"
            for i, memory in enumerate(agent_memories, 1):
                memory_context += f"{i}. {memory['content'][:200]}...\n"
        
        if shared_memories:
            memory_context += "\n**共享知识库**:\n"
            for i, memory in enumerate(shared_memories, 1):
                memory_context += f"{i}. {memory['content'][:150]}...\n"
        
        return memory_context
    
    async def clear_agent_memory(self, agent_type: AgentType):
        """清空特定智能体的记忆"""
        if agent_type in self.agent_memories:
            try:
                # 这里需要根据具体的Memory实现来清空
                # ChromaDBVectorMemory可能需要重新创建collection
                pass
            except Exception as e:
                print(f"清空智能体记忆失败: {e}")
    
    async def get_memory_statistics(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        stats = {
            "agent_memories": {},
            "shared_memory_count": 0,
            "total_memories": 0
        }
        
        try:
            # 统计各智能体记忆数量
            for agent_type, memory in self.agent_memories.items():
                # 这里需要根据具体实现获取记忆数量
                stats["agent_memories"][agent_type.value] = 0  # 占位符
            
            # 统计共享记忆数量
            stats["shared_memory_count"] = 0  # 占位符
            
            # 计算总数
            stats["total_memories"] = sum(stats["agent_memories"].values()) + stats["shared_memory_count"]
            
        except Exception as e:
            print(f"获取记忆统计失败: {e}")
        
        return stats


# 全局记忆管理器实例
global_memory_manager = AgentMemoryManager()


class MemoryEnhancedAgent:
    """增强记忆功能的智能体基类"""
    
    def __init__(self, agent_type: AgentType, base_agent):
        self.agent_type = agent_type
        self.base_agent = base_agent
        self.memory_manager = global_memory_manager
    
    async def get_memory_context(self, query: str) -> str:
        """获取记忆上下文"""
        return await self.memory_manager.get_relevant_memories(self.agent_type, query)
    
    async def store_experience(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """存储经验到记忆"""
        await self.memory_manager.add_agent_memory(self.agent_type, content, metadata)
    
    async def enhanced_run(self, task: str, use_memory: bool = True) -> Any:
        """增强的运行方法，集成记忆功能"""
        enhanced_task = task
        
        if use_memory:
            # 获取相关记忆
            memory_context = await self.get_memory_context(task)
            
            if memory_context:
                enhanced_task = f"""
{task}

{memory_context}

请结合以上相关经验来完成任务。
"""
        
        # 调用原始智能体
        result = await self.base_agent.run(task=enhanced_task)
        
        # 存储本次执行经验
        if use_memory:
            experience = f"任务: {task}\n结果: {str(result)[:500]}..."
            await self.store_experience(
                experience,
                {
                    "task_type": "execution",
                    "timestamp": datetime.now().isoformat(),
                    "success": True
                }
            )
        
        return result


def create_memory_enhanced_agent(agent_type: AgentType, base_agent) -> MemoryEnhancedAgent:
    """创建增强记忆功能的智能体"""
    return MemoryEnhancedAgent(agent_type, base_agent)
