"""
系统配置管理模块
"""
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field
import os
from pathlib import Path


class Settings(BaseSettings):
    """系统配置类"""
    
    # Ollama配置 (本地模型)
    ollama_base_url: str = Field(default="http://localhost:11434", env="OLLAMA_BASE_URL")
    ollama_model: str = Field(default="deepseek-r1:1.5b", env="OLLAMA_MODEL")
    
    # OpenAI配置 (备用)
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4o-mini", env="OPENAI_MODEL")
    
    # Azure OpenAI配置 (备用)
    azure_openai_api_key: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_KEY")
    azure_openai_endpoint: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    azure_openai_api_version: str = Field(default="2024-02-15-preview", env="AZURE_OPENAI_API_VERSION")
    
    # 模型选择策略 (ollama/openai/azure)
    model_provider: str = Field(default="ollama", env="MODEL_PROVIDER")
    
    # ChromaDB配置
    chroma_db_path: str = Field(default="./data/chroma_db", env="CHROMA_DB_PATH")
    chroma_collection_name: str = Field(default="workflow_memory", env="CHROMA_COLLECTION_NAME")
    
    # 系统配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    max_concurrent_agents: int = Field(default=4, env="MAX_CONCURRENT_AGENTS")
    workflow_timeout: int = Field(default=300, env="WORKFLOW_TIMEOUT")
    
    # 智能体配置 (针对本地模型调整温度参数)
    requirement_analyzer_temperature: float = Field(default=0.1, env="REQUIREMENT_ANALYZER_TEMPERATURE")
    requirement_reviewer_temperature: float = Field(default=0.1, env="REQUIREMENT_REVIEWER_TEMPERATURE")
    testcase_generator_temperature: float = Field(default=0.2, env="TESTCASE_GENERATOR_TEMPERATURE")
    testcase_reviewer_temperature: float = Field(default=0.1, env="TESTCASE_REVIEWER_TEMPERATURE")
    
    # 本地模型特定配置
    ollama_timeout: int = Field(default=60, env="OLLAMA_TIMEOUT")
    ollama_max_tokens: int = Field(default=4096, env="OLLAMA_MAX_TOKENS")
    ollama_context_length: int = Field(default=8192, env="OLLAMA_CONTEXT_LENGTH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @property
    def chroma_db_full_path(self) -> Path:
        """获取ChromaDB完整路径"""
        return Path(self.chroma_db_path).resolve()
    
    def ensure_data_dirs(self):
        """确保数据目录存在"""
        self.chroma_db_full_path.mkdir(parents=True, exist_ok=True)
    
    @property
    def is_using_ollama(self) -> bool:
        """是否使用Ollama本地模型"""
        return self.model_provider.lower() == "ollama"
    
    @property
    def is_using_openai(self) -> bool:
        """是否使用OpenAI"""
        return self.model_provider.lower() == "openai"
    
    @property
    def is_using_azure(self) -> bool:
        """是否使用Azure OpenAI"""
        return self.model_provider.lower() == "azure"


# 全局配置实例
settings = Settings()